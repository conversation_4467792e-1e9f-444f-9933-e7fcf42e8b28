@import "bootstrap.min.css";
@import "plugins/nice-select.css";
@import "plugins/select2.min.css";
@import "plugins/swiper.min.css";
@import "plugins/jquery-ui-min.css";
@import "plugins/odometer-min.css";
@import "plugins/animate.css";
@import "plugins/quill.snow.css";
@import "https://fonts.googleapis.com/css2?family=Lexend:wght@200;300;400;500;600;700;800&amp;family=Roboto:wght@300;400;500;700;900&amp;display=swap";
@import "https://cdnjs.cloudflare.com/ajax/libs/tabler-icons/1.35.0/iconfont/tabler-icons.min.css";

:root {
    --body-font: "Lexend", sans-serif;
    --roboto-font: "Roboto", sans-serif;
    --transition: all 0.5s ease-in-out;
    --transition2: all 0.3s ease-in-out;
    --animate-duration: 1s;
    --animate-delay: 0.5s;
    --p1: 253, 183, 183;
    --p2: 253, 183, 183;
    --p3: 245, 149, 113;
    --p4: 11, 11, 18;
    --p5: 22, 22, 24;
    --p6: 255, 255, 255;
    /*--p7: 40, 61, 31;*/
    --p7: 33, 28, 50;
    --p8: 199, 248, 1;
    --p9: 33, 28, 50;
    --s1: 235, 235, 236;
    --s2: 253, 183, 183;
    --gradient-p1: linear-gradient(180deg, rgba(199, 248, 1, 0.65) 0%, rgba(250, 205, 149, 0.65) 100%);
    --gradient-p2: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    --gradient-p3: linear-gradient(180deg, rgba(188, 255, 164, 0.40) -2.24%, rgba(152, 216, 129, 0.25) 60.6%, rgba(90, 190, 54, 0.04) 100%);
    --box-shadow-p1: 0px 0px 24px 0px rgba(0, 0, 0, 0.08)
}

.display-one {
    font-size: 80px;
    line-height: 120%
}

.display-two {
    font-size: 72px;
    line-height: 120%
}

.display-three {
    font-size: 64px;
    line-height: 120%
}

.display-four {
    font-size: 56px;
    line-height: 120%
}

.fs-one, h1, h1 > a {
    font-size: 48px;
    line-height: 120%
}

.fs-two, h2, h2 > a {
    font-size: 40px;
    line-height: 120%
}

.fs-three, h3, h3 > a {
    font-size: 32px;
    line-height: 120%
}

.fs-four, h4, h4 > a {
    font-size: 24px;
    line-height: 130%
}

.fs-five, h5, h5 > a {
    font-size: 20px;
    line-height: 130%
}

.fs-six, h6, h6 > a {
    font-size: 16px;
    line-height: 130%
}

.fs-seven {
    font-size: 14px;
    line-height: 130%
}

.fs-eight {
    font-size: 12px;
    line-height: 130%
}

.fs-nine {
    font-size: 10px;
    line-height: 130%
}

.fs-ten {
    font-size: 18px;
    line-height: 130%
}

table thead tr th {
    font-size: 16px;
    line-height: 130%
}

a, p, button, span, label, li {
    font-size: 16px;
    line-height: 150%
}

@media (max-width: 1399px) {
    .display-zero {
        font-size: 82px;
        font-weight: 120
    }
}

@media (max-width: 1199px) {
    .display-zero {
        font-size: 72px;
        font-weight: 120
    }

    h1, h1 > a {
        font-size: 40px;
        line-height: 120%
    }

    h2, h2 > a {
        font-size: 32px;
        line-height: 120%
    }

    .preloader {
        background-size: 30%
    }
}

.pt-120 {
    padding-top: 120px
}

.pb-120 {
    padding-bottom: 120px
}

@media (max-width: 991px) {
    .display-zero {
        font-size: 72px;
        line-height: 120%
    }

    .display-one {
        font-size: 72px;
        line-height: 120%
    }

    .display-two {
        font-size: 64px;
        line-height: 120%
    }

    .display-three {
        font-size: 56px;
        line-height: 120%
    }

    .display-four {
        font-size: 48px;
        line-height: 120%
    }

    .display-five {
        font-size: 40px;
        line-height: 120%
    }

    .display-six {
        font-size: 32px;
        line-height: 120%
    }

    .fs-one, h1, h1 > a {
        font-size: 40px;
        line-height: 120%
    }

    .fs-two, h2, h2 > a {
        font-size: 32px;
        line-height: 120%
    }

    .fs-three, h3, h3 > a {
        font-size: 24px;
        line-height: 130%
    }

    .fs-four, h4, h4 > a {
        font-size: 20px;
        line-height: 130%
    }

    .fs-five, h5, h5 > a {
        font-size: 16px;
        line-height: 130%
    }

    .fs-six, h6, h6 > a {
        font-size: 14px;
        line-height: 130%
    }

    .fs-seven {
        font-size: 12px;
        line-height: 130%
    }

    .fs-ten {
        font-size: 16px;
        line-height: 130%
    }

    table thead tr th {
        font-size: 14px;
        line-height: 130%
    }

    a, p, span, button, label, li {
        font-size: 14px;
        line-height: 22px
    }

    .pt-120 {
        padding-top: 60px
    }

    .pb-120 {
        padding-bottom: 60px
    }

    .banner-section .overlay .sec-img {
        display: none
    }

    .banner-section .banner-content {
        padding: 80px 0 90px
    }
}

@media (max-width: 575px) {
    .display-zero {
        font-size: 56px;
        line-height: 120%
    }

    .display-one {
        font-size: 56px;
        line-height: 67.2px
    }

    .display-two {
        font-size: 48px;
        line-height: 46.4px
    }

    .display-three {
        font-size: 42px;
        line-height: 54.6px
    }

    .display-four {
        font-size: 32px;
        line-height: 38.4px
    }

    .display-five {
        font-size: 24px;
        line-height: 31.2px
    }

    .display-six {
        font-size: 32px;
        line-height: 38.4px
    }

    .pt-120 {
        padding-top: 50px
    }

    .pb-120 {
        padding-bottom: 50px
    }
}

@media (max-width: 480px) {
    .display-zero {
        font-size: 38px;
        line-height: 120%
    }

    .display-one {
        font-size: 38px;
        line-height: 120%
    }
}

html {
    scroll-behavior: smooth
}

body {
    font-family: var(--body-font);
    color: #dfdfe0;
    font-size: 16px;
    line-height: 130%;
    padding: 0;
    margin: 0;
    font-weight: 400;
    overflow-x: hidden;
    position: relative
}

section, footer {
    overflow: hidden;
    overflow-x: hidden !important
}

a, p, button, span, label, li {
    margin: 0;
    font-family: var(--body-font);
    color: rgba(var(--s1), 1);
    font-weight: 400
}

a {
    text-decoration: none;
    outline: none;
    color: #ebebec;
    transition: var(--transition)
}

a:focus, a:hover {
    text-decoration: none;
    outline: none;
    color: rgba(var(--p6), 1)
}

h1, h1 > a, h2, h2 > a, h3, h3 > a, h4, h4 > a, h5, h5 > a, h6, h6 > a, .display-zero, .display-one, .display-two, .display-three, .display-four, .display-five, .display-six, .heading {
    font-family: var(--body-font);
    color: rgba(var(--p6), 1);
    font-weight: 700;
    padding: 0;
    margin: 0;
    line-height: 120%;
    display: block;
    transition: var(--transition)
}

h1 span, h1 > a span, h2 span, h2 > a span, h3 span, h3 > a span, h4 span, h4 > a span, h5 span, h5 > a span, h6 span, h6 > a span, .display-zero span, .display-one span, .display-two span, .display-three span, .display-four span, .display-five span, .display-six span, .heading span {
    font-size: inherit;
    font-family: inherit;
    color: inherit;
    line-height: inherit;
    font-weight: inherit
}

.roboto {
    font-family: var(--roboto-font)
}

img {
    max-width: 100%;
    height: auto
}

ul, ol {
    padding: 0;
    margin: 0;
    list-style: none
}

ul li, ol li {
    font-size: inherit;
    font-weight: inherit
}

button {
    border: none;
    cursor: pointer;
    font-size: 16px;
    line-height: 130%;
    padding: 0;
    background-color: rgba(0, 0, 0, 0)
}

input, textarea {
    padding: 12px 20px;
    color: rgba(var(--db), 1);
    width: 100%;
    font-family: var(--body-font);
    outline-color: rgba(0, 0, 0, 0);
    font-size: 16px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid rgba(var(--b1), 0.3)
}

input::placeholder, textarea::placeholder {
    color: rgba(var(--db), 0.4);
    font-size: 16px
}

input:focus, textarea:focus {
    outline: 1px solid rgba(var(--b1));
    box-shadow: none
}

input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield
}

input[type=checkbox] {
    width: initial;
    height: initial
}

select {
    padding: 16px;
    border: none;
    cursor: pointer;
    outline: none
}

.max-auto {
    max-width: 100%
}

.max-un {
    max-width: unset
}

@media (min-width: 1400px) {
    .max-xxl-un {
        max-width: unset
    }
}

@media (min-width: 1200px) {
    .max-xl-un {
        max-width: unset
    }
}

@media (min-width: 992px) {
    .max-lg-un {
        max-width: unset
    }
}

@media (min-width: 768px) {
    .max-md-un {
        max-width: unset
    }
}

.d-rtl {
    direction: rtl
}

.collapsing {
    transition: var(--transition)
}

.pt120 {
    padding-top: 120px
}

.pb120 {
    padding-bottom: 120px
}

.ml-316 {
    margin-left: 316px
}

@media (max-width: 991px) {
    .ml-316 {
        margin-left: 12px
    }
}

.alt-color a, .alt-color p, .alt-color span, .alt-color h1, .alt-color h2, .alt-color h3, .alt-color h4, .alt-color h5, .alt-color h6, .alt-color i, .alt-color select, .alt-color button {
    color: rgba(var(--p7), 1)
}

.p1-color {
    color: rgba(var(--p1), 1)
}

.p2-color {
    color: rgba(var(--p2), 1)
}

.p3-color {
    color: rgba(var(--p3), 1)
}

.p4-color {
    color: rgba(var(--p4), 1)
}

.p5-color {
    color: rgba(var(--p5), 1)
}

.p6-color {
    color: rgba(var(--p6), 1)
}

.p7-color {
    color: rgba(var(--p7), 1)
}

.p8-color {
    color: rgba(var(--p8), 1)
}

.s1-color {
    color: rgba(var(--s1), 1)
}

.s2-color {
    color: rgba(var(--s2), 1)
}

.bg1-color {
    background-color: rgba(var(--p6), 0.08)
}

.bg2-color {
    background-color: rgba(var(--p1), 1)
}

.bg3-color {
    background-color: rgba(var(--p6), 1)
}

.bg4-color {
    background-color: rgba(var(--p4), 1)
}

.bg5-color {
    background-color: rgba(var(--p5), 1)
}

.bg6-color {
    background-color: rgba(var(--p6), 1)
}

.bg7-color {
    background-color: rgba(var(--p7), 1)
}

.bg8-color {
    background-color: rgba(var(--p2), 1)
}

.bg9-color {
    background-color: rgba(var(--p9), 1)
}

.br1 {
    border: 1px solid rgba(var(--p8), 1)
}

.br2 {
    border: 1px solid rgba(var(--p6), 0.2)
}

.br3 {
    border: 1px solid rgba(var(--p7), 1)
}

.br4 {
    border: 1px solid rgba(var(--p1), 1)
}

.br5 {
    border: 1px solid rgba(var(--p2), 1)
}

.box-shadow1 {
    box-shadow: var(--box-shadow-p1)
}

.box-shadow5 {
    box-shadow: var(--box-shadow-p2)
}

.rounded-item {
    border-radius: 50%
}

.rounded-item2 {
    border-radius: 42%
}

.rounded-20 {
    border-radius: 20px
}

.box-shadow3 {
    box-shadow: var(--box-shadow-p1)
}

.fw_500 {
    font-weight: 500
}

.text_grdt {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.mx-ch {
    max-width: 60ch
}

.none_two {
    display: none !important
}

.fadeInUp {
    animation-duration: var(--animate-duration);
    animation-delay: var(--animate-delay)
}

.cmn-btn {
    position: relative;
    display: inline-block;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    color: #283d1f;
    background: #fdb7b7;
    cursor: pointer;
    transition: ease-out .5s;
    border: 2px solid #fdb7b7;
    border-radius: 10px;
    box-shadow: inset 0 0 0 0 #0b0b12;
    overflow: hidden
}

.cmn-btn span, .cmn-btn i {
    transition: .3s linear
}

@media (max-width: 991px) {
    .cmn-btn {
        font-size: 15px
    }
}

@media (max-width: 575px) {
    .cmn-btn {
        font-size: 14px
    }
}

.cmn-btn:hover {
    transition: .3s linear;
    color: #fff;
    background: rgba(0, 0, 0, 0);
    border-radius: 10px;
    box-shadow: inset 0 -100px 0 0 rgba(0, 0, 0, 0)
}

.cmn-btn:hover span {
    color: #fff
}

.cmn-btn:active {
    transform: scale(0.9);
    border-radius: 10px
}

.cmn-btn::before {
    content: "";
    transition: .5s all ease;
    position: absolute;
    top: 0;
    left: 50%;
    right: 50%;
    bottom: 0;
    opacity: 0;
    background-color: rgba(var(--s2), 1);
    z-index: -1;
    color: rgba(var(--p2), 1)
}

.cmn-btn:hover::before, .cmn-btn:focus::before {
    transition: .5s all ease;
    left: 0;
    right: 0;
    opacity: 1;
    color: rgba(var(--p2), 1)
}

.cmn-btn.second-alt {
    background-color: #283d1f;
    transition: .5s all ease;
    color: #fff;
    border: 1px solid #283d1f
}

.cmn-btn.second-alt::before, .cmn-btn.second-alt::after {
    transition: .5s all ease;
    background-color: rgb(var(--p1))
}

.cmn-btn.second-alt:hover {
    color: rgba(var(--p4), 1);
    background-color: rgba(0, 0, 0, 0)
}

.cmn-btn.third-alt {
    background-color: rgba(0, 0, 0, 0);
    color: rgb(var(--p2));
    transition: .5s linear
}

.cmn-btn.third-alt i {
    color: rgb(var(--p2))
}

.cmn-btn.third-alt::before, .cmn-btn.third-alt::after {
    background-color: rgb(var(--p1))
}

.cmn-btn.third-alt:hover, .cmn-btn.third-alt:focus, .cmn-btn.third-alt.active {
    color: rgb(var(--p4));
    background-color: rgb(var(--p1))
}

.cmn-btn.third-alt:hover i, .cmn-btn.third-alt:focus i, .cmn-btn.third-alt.active i {
    color: rgb(var(--p4))
}

.cmn-btn.fourth-alt {
    background-color: rgba(255, 255, 255, .08);
    color: rgb(var(--p1)) !important;
    border: 1px solid rgba(255, 255, 255, .2)
}

.cmn-btn.fourth-alt::before, .cmn-btn.fourth-alt::after {
    color: rgb(var(--p2));
    background-color: rgb(var(--db))
}

.cmn-btn.fourth-alt:hover, .cmn-btn.fourth-alt.active {
    color: rgb(var(--p2))
}

.cmn-btn.fourth-alt:hover i, .cmn-btn.fourth-alt.active i {
    color: rgb(var(--p2))
}

.cmn-btn.fifth-alt {
    background-color: rgba(var(--p4), 1);
    color: rgb(var(--p6)) !important;
    border: 1px solid rgba(var(--bg1), 1)
}

.cmn-btn.fifth-alt i {
    color: rgb(var(--p6))
}

.cmn-btn.fifth-alt::before, .cmn-btn.fifth-alt::after {
    background-color: rgb(var(--p1))
}

.cmn-btn.fifth-alt:hover, .cmn-btn.fifth-alt:focus, .cmn-btn.fifth-alt.active {
    color: rgb(var(--p4)) !important;
    background-color: rgb(var(--p1))
}

.cmn-btn.fifth-alt:hover i, .cmn-btn.fifth-alt:focus i, .cmn-btn.fifth-alt.active i {
    color: rgb(var(--p4))
}

.cmn-btn.six-alt {
    background-color: #f3f9fa;
    color: inherit
}

.cmn-btn.six-alt::before, .cmn-btn.six-alt::after {
    color: rgb(var(--p2));
    background-color: rgb(var(--db))
}

.cmn-btn.six-alt:hover, .cmn-btn.six-alt.active {
    color: rgb(var(--p2))
}

.cmn-btn.six-alt:hover i, .cmn-btn.six-alt.active i {
    color: rgb(var(--p2))
}

.cmn-btn.seven-alt {
    background-color: rgb(var(--bg1));
    transition: .5s all ease;
    color: rgba(var(--db), 1) !important
}

.cmn-btn.seven-alt::before, .cmn-btn.seven-alt::after {
    transition: .5s all ease;
    background-color: rgb(var(--s2))
}

.cmn-btn.seven-alt:hover {
    color: rgba(var(--db), 1)
}

.tabcontents {
    position: relative
}

.tabitem {
    transform: translateY(100px);
    position: absolute;
    z-index: -1;
    top: 0;
    width: 100%;
    opacity: 0;
    transition: .5s all
}

.tabitem.active {
    position: initial;
    z-index: 1;
    opacity: 1;
    transform: translateY(0)
}

.cpoint {
    cursor: pointer
}

.exhvr:hover {
    background-color: rgba(var(--db), 1) !important;
    color: rgba(var(--s2), 1) !important
}

.file-upload {
    border-radius: 48px;
    border: 1px solid rgba(var(--p1), 1)
}

.file-upload input {
    display: none
}

.file-upload .file {
    cursor: pointer;
    position: relative
}

.file-upload .file .file-custom {
    border-radius: 20px
}

.file-upload .add-file {
    color: rgba(var(--p1), 1)
}

.hvr {
    transition: .3s linear
}

.hvr:hover {
    background-color: #2a235b;
    color: rgba(var(--p2), 1)
}

.btn-popup-animation {
    width: 80px;
    min-width: 80px;
    height: 80px;
    z-index: 0;
    transition: var(--transition)
}

.btn-popup-animation i {
    color: rgb(var(--p2-fixed))
}

.btn-popup-animation:before {
    content: "";
    position: absolute;
    width: 85%;
    height: 85%;
    background-color: rgb(var(--p1));
    border-radius: 50%;
    opacity: .6;
    z-index: -1;
    animation: pulse 2s ease-out infinite
}

.btn-popup-animation:after {
    content: "";
    position: absolute;
    width: 85%;
    height: 85%;
    background-color: rgb(var(--p1));
    border-radius: 50%;
    opacity: .6;
    z-index: -1;
    animation: pulse 2s 1s ease-out infinite
}

@media (max-width: 576px) {
    .btn-popup-animation {
        width: 60px;
        min-width: 60px;
        height: 60px
    }

    .btn-popup-animation i {
        color: rgb(var(--p2-fixed))
    }
}

@keyframes pulse {
    100% {
        transform: scale(2);
        opacity: 0
    }
}

.circle-text {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: rgba(var(--s3), 1);
    border: 1px solid rgba(var(--p2), 1);
    position: relative
}

.circle-text .text {
    animation: rotate 50s linear infinite;
    width: 100%;
    height: 100%;
    position: absolute
}

.circle-text .text p {
    transform: translate(50%, 5px)
}

.circle-text .text span {
    position: absolute;
    color: rgba(var(--p2), 1);
    font-size: inherit;
    font-weight: 600;
    transform-origin: 0 70px
}

.circle-text:hover .text {
    animation-play-state: paused
}

.circle-text.second {
    width: 120px;
    height: 120px;
    background-color: rgba(var(--n2), 1);
    border-color: rgba(0, 0, 0, 0)
}

.circle-text.second .text p {
    transform: translate(50%, 5px)
}

.circle-text.second .text span {
    transform-origin: 0 55px
}

.circle-text.third {
    width: 880px;
    height: 880px;
    border-color: rgba(0, 0, 0, 0)
}

.circle-text.third .text p {
    transform: translate(50%, 5px)
}

.circle-text.third .text span {
    transform-origin: 0 440px
}

.v-border {
    background: rgba(var(--p1), 0.2);
    width: 1px;
    height: 25px
}

.v-line::before {
    position: absolute;
    content: "";
    width: 1px;
    height: 40px;
    background-color: rgb(var(--p7))
}

.v-line.lg::before {
    height: 15px
}

.v-line.lgx::before {
    height: 110px;
    width: 2px;
    background-color: rgba(255, 255, 255, .2)
}

.v-line.lg2::before {
    height: 17px;
    padding: 1px;
    background-color: rgba(var(--s2), 1) !important
}

.v-line.xl::before {
    background-color: rgb(var(--p7)) !important;
    height: 15px
}

.v-line.sm::before {
    height: 25px
}

.v-line.smx::before {
    width: 1px;
    height: 20px;
    background: rgba(255, 255, 255, .2)
}

.v-line.dashed::before {
    border-right: 1px dashed rgb(var(--n5));
    background: rgba(0, 0, 0, 0)
}

.cus-border {
    border-color: rgba(var(--p1), 1) !important
}

.cus-border.second {
    border-color: rgba(var(--p1), 0.1) !important
}

.cus-border.third {
    border-color: rgb(var(--n5)) !important
}

.border-bottom-dash {
    border-bottom: 1px dashed rgba(0, 0, 0, 0)
}

.ul-dots li {
    list-style: disc
}

.ul-decimal li {
    list-style: decimal
}

.social-area a {
    width: 40px;
    min-width: 40px;
    height: 40px;
    border: 1px solid rgba(var(--p1), 0.1);
    border-radius: 50%;
    background: rgb(var(--p1));
    display: block;
    transition: var(--transition)
}

.social-area a i {
    color: rgba(var(--p2-fixed), 1)
}

.social-area a:hover {
    background-color: rgba(0, 0, 0, 0)
}

.social-area a:hover i {
    color: rgba(var(--p1), 1)
}

.social-area.alt a {
    border-color: rgba(var(--p1), 1);
    background: rgba(0, 0, 0, 0)
}

.social-area.alt a:hover {
    background-color: rgba(var(--p1), 1)
}

.social-items a i, .social-items a span {
    transition: var(--transition)
}

.social-items a:hover i, .social-items a:hover span {
    color: rgba(var(--p2), 1)
}

.mouse-follower {
    z-index: 10
}

.mouse-follower span {
    z-index: 10;
    opacity: 1;
    top: -10px;
    left: -10px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    position: fixed;
    transition: 100ms ease-in-out;
    pointer-events: none
}

.mouse-follower .cursor-outline {
    height: 30px;
    width: 30px;
    border: 1px solid rgba(var(--p1), 1)
}

.mouse-follower .cursor-dot {
    height: 6px;
    width: 6px;
    background: rgba(var(--p1), 1)
}

.mouse-follower.hide-cursor span {
    border-color: rgba(0, 0, 0, 0);
    background: rgba(0, 0, 0, 0)
}

span {
    display: inline-block
}

.table thead th, .table td, .table th {
    border: none
}

.ovf-unset {
    overflow-x: unset !important
}

iframe {
    border: 0;
    width: 100%
}

*::-webkit-scrollbar-track {
    background: rgba(var(--n5), 1)
}

*::-webkit-scrollbar {
    width: 5px
}

*::-webkit-scrollbar-button, *::-webkit-scrollbar-thumb {
    background: rgba(var(--s2), 1)
}

.cus-scrollbar {
    overflow-y: auto;
    overflow-x: hidden;
    position: sticky;
    max-height: 90vh;
    top: 100px
}

.cus-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0)
}

.cus-scrollbar::-webkit-scrollbar {
    width: 3px;
    background-color: rgba(0, 0, 0, 0)
}

.cus-scrollbar::-webkit-scrollbar-button, .cus-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0)
}

.cus-scrollbar:hover::-webkit-scrollbar {
    width: 3px
}

.cus-scrollbar:hover::-webkit-scrollbar-track {
    background: rgba(255, 211, 198, .5)
}

.cus-scrollbar:hover::-webkit-scrollbar-button, .cus-scrollbar:hover::-webkit-scrollbar-thumb {
    background: rgba(var(--s2), 1)
}

.d-center {
    display: flex;
    justify-content: center;
    align-items: center
}

.cus-nz1 {
    z-index: -1
}

.cus-z0 {
    z-index: 0
}

.cus-z1 {
    z-index: 1
}

.cus-z2 {
    z-index: 2
}

.scrollToTop {
    position: fixed;
    bottom: 0;
    width: 40px;
    height: 40px;
    right: 30px;
    background-color: rgba(var(--s2), 1);
    padding: 0 12px;
    color: rgba(var(--p2), 1);
    line-height: 120%;
    font-size: 12px;
    border-radius: 5px;
    text-align: center;
    z-index: 9;
    cursor: pointer;
    transition: all 1s;
    transform: translateY(100%)
}

.scrollToTop i {
    transition: .3s linear
}

.scrollToTop.active {
    bottom: 30px;
    transform: translateY(0%)
}

.scrollToTop:hover i {
    margin-bottom: 7px
}

.pre-item {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 9999;
    background: rgba(var(--p9), 1);
    height: 100%;
    width: 100%
}

.loaderall {
    position: relative;
    border: 24px solid;
    border-color: #0b0b12 rgba(0, 0, 0, 0) #525d2f rgba(0, 0, 0, 0);
    animation: rotate_000 2s linear infinite
}

.loaderall:before, .loaderall:after {
    content: "";
    display: block;
    border: 24px solid rgba(0, 0, 0, 0);
    border-left-color: #283d1f;
    position: absolute;
    left: -24px;
    top: -24px;
    animation: prix 1s infinite ease-in;
    transform-origin: 0% 100%
}

.loaderall:before {
    border-color: rgba(0, 0, 0, 0) #fdb7b7 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
    transform-origin: 100% 0%;
    animation-delay: .5s
}

@keyframes rotate_000 {
    100% {
        transform: rotate(360deg)
    }
}

@keyframes prix {
    20%, 80% {
        transform: rotate(0)
    }
    50% {
        transform: rotate(-90deg)
    }
}

.singleTab .nav-links {
    cursor: pointer
}

.singleTab .tabContents {
    position: relative
}

.singleTab .tabContents .tabItem {
    transform: translateY(100px);
    position: absolute;
    z-index: -1;
    top: 0;
    width: 100%;
    opacity: 0;
    transition: .8s all
}

.singleTab .tabContents .tabItem.active {
    position: initial;
    z-index: 1;
    opacity: 1;
    transform: translateY(0)
}

.singleTab.pros-cros .nav-item {
    border-radius: unset;
    background: rgba(12, 169, 64, .7);
    font-weight: 700;
    color: rgba(var(--p2))
}

.singleTab.pros-cros .nav-item .active {
    background: rgba(var(--p1))
}

.singleTab.pros-cros .nav-item button {
    color: rgb(var(--p2-fixed));
    transition: var(--transition)
}

.search-area::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 50%;
    bottom: 0;
    left: 0;
    z-index: -1
}

.search-area .search-box {
    display: grid;
    gap: 16px;
    grid-template-columns:repeat(5, 1fr)
}

@media (max-width: 1199px) {
    .search-area .search-box {
        grid-template-columns:repeat(3, 1fr)
    }
}

@media (max-width: 767px) {
    .search-area .search-box {
        grid-template-columns:repeat(2, 1fr)
    }
}

@media (max-width: 575px) {
    .search-area .search-box {
        grid-template-columns:repeat(1, 1fr)
    }
}

.pagination li a {
    color: rgba(var(--n2), 1)
}

.pagination .page-item .page-link {
    outline: none;
    bottom: 0;
    padding: 0;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px;
    z-index: 999
}

.pagination .page-item .page-link:hover, .pagination .page-item .page-link.active, .pagination .page-item .page-link:focus {
    background-color: rgba(var(--p1), 1);
    border-color: rgba(0, 0, 0, 0);
    box-shadow: none
}

.breadcrumb-area .breadcrumb {
    row-gap: 8px
}

.breadcrumb-area .breadcrumb a, .breadcrumb-area .breadcrumb span {
    color: rgba(var(--db), 1);
    font-size: 18px;
    font-weight: 500
}

@media (max-width: 991px) {
    .breadcrumb-area .breadcrumb a, .breadcrumb-area .breadcrumb span {
        font-size: 16px
    }
}

.breadcrumb-area .breadcrumb .breadcrumb-item + .breadcrumb-item {
    position: relative
}

.breadcrumb-area .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "chevron_right";
    font-family: "Material Symbols Outlined";
    z-index: 1;
    position: absolute;
    left: 0;
    font-size: 24px;
    color: rgba(var(--db), 1)
}

.breadcrumb-area .breadcrumb .active span {
    color: rgba(var(--p1), 1)
}

.nice-select {
    background-color: rgba(0, 0, 0, 0);
    border: rgba(0, 0, 0, 0);
    float: initial;
    overflow: initial;
    height: initial;
    padding: 0;
    display: inline-flex;
    align-items: center;
    line-height: 150%;
    width: 100%
}

.nice-select:focus, .nice-select:hover {
    border-color: rgba(0, 0, 0, 0)
}

.nice-select::after {
    height: 10px;
    width: 10px;
    right: 15px;
    top: initial;
    border-color: rgba(var(--n2), 0.6)
}

.nice-select .list {
    width: initial;
    box-shadow: none;
    overflow: initial;
    width: 100%;
    top: 100%;
    padding: 0;
    box-shadow: var(--shadow-sm);
    max-height: 60vh;
    overflow-x: auto
}

.nice-select .option {
    background-color: rgba(0, 0, 0, 0);
    font-size: 16px;
    line-height: 150%;
    padding: 4px 5px;
    min-height: initial;
    font-weight: 500
}

.nice-select .option:hover, .nice-select .option:focus, .nice-select .option.selected.focus {
    background-color: rgba(0, 0, 0, 0)
}

.nice-select .current {
    font-weight: 500
}

.checkbox-single {
    position: relative;
    cursor: pointer;
    user-select: none
}

.checkbox-single input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0
}

.checkbox-single .checkmark {
    position: absolute;
    left: 0;
    height: 20px;
    width: 20px;
    border-radius: 20px;
    border: 1px solid #e1e0e7
}

.checkbox-single .checkmark::after {
    content: ""
}

.checkbox-single input:checked ~ .checkmark::after {
    content: "done";
    position: absolute;
    font-family: "Material Symbols Outlined";
    color: rgb(var(--s2))
}

.checkbox-single input:checked ~ .checkmark:after {
    display: block
}

.input[type=checkbox], input[type=radio] {
    accent-color: #ec604f !important
}

.switch-wrapper .active {
    color: rgba(var(--p1), 1)
}

.switch-wrapper .switch {
    width: 50px;
    height: 26px
}

.switch-wrapper .switch .slider {
    cursor: pointer;
    border-radius: 34px;
    transition: var(--transition);
    background: rgba(var(--p1), 1)
}

.switch-wrapper .switch .slider::before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    border-radius: 50%;
    transform: translateX(3px);
    background-color: rgba(var(--p2-fixed), 1);
    transition: var(--transition)
}

.switch-wrapper .switch input:checked + .slider::before {
    transform: translateX(25px)
}

.single-radio {
    cursor: pointer;
    user-select: none;
    position: relative
}

.single-radio:hover input ~ .checkmark {
    background-color: rgba(0, 0, 0, 0)
}

.single-radio .checkmark {
    position: absolute;
    left: 0;
    height: 16px;
    width: 16px;
    border: 1px solid rgba(var(--p1), 1);
    border-radius: 50%
}

.single-radio .checkmark::after {
    content: "";
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0)
}

.single-radio input:checked ~ .checkmark {
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid rgba(var(--p2), 1)
}

.single-radio input:checked ~ .checkmark::after {
    display: block;
    background: rgba(var(--p2), 1)
}

.slide-button {
    outline: none;
    bottom: 0;
    padding: 0;
    width: 48px;
    height: 48px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px
}

.slide-button.ara-prev {
    transform: rotate(180deg)
}

.slider-pagination {
    width: initial
}

.slider-pagination span {
    letter-spacing: 10px;
    color: rgba(var(--s2), 1)
}

.input-area {
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid rgba(var(--b1))
}

.input-area:focus-within {
    border: 1px solid solid;
    outline: 1px solid rgba(var(--b1));
    border-color: rgba(0, 0, 0, 0)
}

.input-area input, .input-area textarea {
    border-radius: 0;
    padding: 0;
    border: none;
    outline: none;
    background: rgba(0, 0, 0, 0)
}

.input-area input::placeholder, .input-area textarea::placeholder {
    color: rgba(var(--db));
    font-weight: 500
}

.input-area .nice-select {
    padding-right: 40px !important
}

.input-area .nice-select::placeholder {
    color: rgba(var(--db), 1)
}

.input-area .nice-select:focus, .input-area .nice-select:hover {
    border: none;
    outline: none;
    color: rgba(var(--db), 1)
}

.input-area .nice-select .current {
    color: rgba(var(--db), 1);
    font-size: 16px;
    border: none;
    outline: none
}

.input-area .nice-select .list {
    border: 1px solid rgba(var(--n6), 0.2);
    bottom: initial;
    padding: 8px 16px;
    background: rgb(var(--bg1));
    color: rgba(var(--db), 1)
}

.input-area .nice-select::after {
    display: block;
    right: 24px
}

.input-area.second {
    border-radius: 48px;
    border: 1px solid rgba(var(--b1))
}

.input-area.second input {
    font-size: 16px
}

.leftright-animation {
    animation: previewShapeX 4s linear infinite
}

.leftright-animation2 {
    animation: previewShapeX 7s linear infinite
}

@keyframes previewShapeX {
    0% {
        transform: translateX(0)
    }
    50% {
        transform: translateX(15px)
    }
    100% {
        transform: translateX(0)
    }
}

@keyframes progress-animate {
    0% {
        width: 0
    }
}

.rotated_bigsmall {
    animation: bigSmall 7s linear infinite
}

@keyframes bigSmall {
    0% {
        transform: scale(1);
        opacity: .7
    }
    50% {
        transform: scale(1.1);
        opacity: 1
    }
    100% {
        transform: scale(1);
        opacity: .7
    }
}

.rotated_bigsmall2 {
    animation: bigSmall2 20s linear infinite
}

@keyframes bigSmall2 {
    0% {
        transform: scale(0.9)
    }
    50% {
        transform: scale(1.1)
    }
    100% {
        transform: scale(0.9)
    }
}

@keyframes previewShapeRounded {
    0% {
        transform: translate(0)
    }
    50% {
        transform: translate(15px)
    }
    100% {
        transform: translate(0)
    }
}

.rotated_animat {
    animation: rotated_animat 4s linear infinite
}

.rotated_animattwo {
    animation: rotated-animattwo 40s linear infinite
}

@keyframes rotated-animattwo {
    100% {
        transform: rotate(1turn)
    }
}

@keyframes rotated_animat {
    0% {
        transform: rotate(0deg) scale(1)
    }
    100% {
        transform: rotate(360deg) scale(1)
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translate3d(0, 50%, 0)
    }
    100% {
        opacity: 1;
        transform: none
    }
}

@keyframes fadeIn1st {
    0% {
        opacity: 0;
        transform: translate3d(10%, 0, 0)
    }
    100% {
        opacity: 1;
        transform: none
    }
}

@keyframes fadeIn2nd {
    0% {
        opacity: 0;
        transform: translate3d(-10%, 0, 0)
    }
    100% {
        opacity: 1;
        transform: none
    }
}

@keyframes rotate {
    100% {
        transform: rotate(360deg)
    }
}

@keyframes prixClipFix {
    0% {
        clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0)
    }
    25% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0)
    }
    50% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%)
    }
    75% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%)
    }
    100% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0)
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -100%, 0)
    }
    to {
        opacity: 1;
        transform: none
    }
}

@keyframes loading {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    100% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }
}

@media (max-width: 1399px) {
    .none_two {
        display: inherit !important
    }
}

.header-section {
    top: 8.5%;
    z-index: 999;
    background-color: rgba(var(--p7), 1);
    border-bottom: 1px solid rgba(var(--p6), 0.16)
}

@media (max-width: 991px) {
    .header-section {
        position: fixed
    }
}

@media (max-width: 1899px) {
    .header-section {
        top: 10.5%
    }
}

@media (max-width: 991px) {
    .header-section {
        top: 0;
        background-color: rgba(var(--p5), 1);
        box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, .3)
    }
}

.header-section.header-fixed {
    border-color: rgba(0, 0, 0, 0);
    box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, .3);
    background-color: rgba(var(--p7), 1);
    position: fixed;
    top: 0
}

.header-section .navbar-toggler {
    padding-right: 0;
    display: none;
    border: none
}

.header-section .navbar-toggler:focus {
    box-shadow: none
}

.header-section .navbar-toggler i {
    color: var(--primary-color)
}

.header-section .navbar-toggler {
    width: 30px;
    height: 6px;
    position: relative;
    right: 15px;
    transition: .3s ease-in-out;
    transform: translateY(-100%)
}

.header-section .navbar-toggler span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: rgba(var(--p2), 1);
    border-radius: 9px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .3s ease-in-out
}

.header-section .navbar-toggler span:nth-child(1) {
    top: 0px
}

.header-section .navbar-toggler span:nth-child(2), .header-section .navbar-toggler span:nth-child(3) {
    top: 12px
}

.header-section .navbar-toggler span:nth-child(4) {
    top: 24px
}

.header-section .navbar-toggler.open span:nth-child(1) {
    top: 18px;
    width: 0%;
    left: 50%
}

.header-section .navbar-toggler.open span:nth-child(2) {
    transform: rotate(45deg)
}

.header-section .navbar-toggler.open span:nth-child(3) {
    transform: rotate(-45deg)
}

.header-section .navbar-toggler.open span:nth-child(4) {
    top: 18px;
    width: 0%;
    left: 50%
}

.header-section .collapsing {
    transition: .5s
}

.header-section .dropdown-menu {
    padding: 5px 15px 15px 15px !important
}

@media (max-width: 991px) {
    .header-section .navbar .navbar-nav {
        margin-top: 0
    }
}

.header-section .navbar .navbar-nav a.active::after {
    content: "";
    width: 100%;
    border-bottom: 2px solid rgb(var(--p1));
    position: absolute;
    top: 100%;
    left: 0
}

.header-section .navbar .navbar-nav a {
    transition: .8s linear !important
}

.header-section .navbar .navbar-nav a:hover::after {
    content: "";
    width: 100%;
    border-bottom: 2px solid rgb(var(--p1));
    position: absolute;
    top: 100%;
    left: 0
}

.header-section .navbar .navbar-nav li {
    position: relative;
    transition: .8s linear !important
}

.header-section .navbar .navbar-nav li a {
    transition: .3s linear !important
}

.header-section .navbar .navbar-nav ul {
    padding: 10px 0;
    padding-right: 15px
}

.header-section .navbar .navbar-nav ul li a {
    color: rgb(var(--p6))
}

.header-section .navbar .navbar-nav a, .header-section .navbar .navbar-nav button {
    text-transform: capitalize;
    font-weight: 500;
    background-color: rgba(0, 0, 0, 0);
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: .3s
}

.header-section .navbar .navbar-nav a.active, .header-section .navbar .navbar-nav a:hover, .header-section .navbar .navbar-nav button.active, .header-section .navbar .navbar-nav button:hover {
    color: rgba(var(--p1), 1)
}

.header-section .navbar .navbar-nav a.active::before, .header-section .navbar .navbar-nav a:hover::before, .header-section .navbar .navbar-nav button.active::before, .header-section .navbar .navbar-nav button:hover::before {
    transform: rotate(-50deg);
    opacity: 1;
    transition: .3s
}

.header-section .navbar .navbar-nav .dropdown-menu, .header-section .navbar .navbar-nav .sub-menu {
    background-color: rgba(var(--p7), 1);
    box-shadow: var(--shadow-lg)
}

.header-section .navbar .navbar-nav .dropdown-menu a, .header-section .navbar .navbar-nav .dropdown-menu button, .header-section .navbar .navbar-nav .sub-menu a, .header-section .navbar .navbar-nav .sub-menu button {
    display: initial
}

.header-section .navbar .navbar-nav .dropdown-menu a:hover, .header-section .navbar .navbar-nav .dropdown-menu button:hover, .header-section .navbar .navbar-nav .sub-menu a:hover, .header-section .navbar .navbar-nav .sub-menu button:hover {
    letter-spacing: 1px;
    transition: .5s
}

.header-section .navbar .navbar-nav .dropdown-menu a::after, .header-section .navbar .navbar-nav .dropdown-menu button::after, .header-section .navbar .navbar-nav .sub-menu a::after, .header-section .navbar .navbar-nav .sub-menu button::after {
    right: initial
}

.header-section .navbar .navbar-nav .dropdown-menu a::before, .header-section .navbar .navbar-nav .dropdown-menu button::before, .header-section .navbar .navbar-nav .sub-menu a::before, .header-section .navbar .navbar-nav .sub-menu button::before {
    display: none
}

.header-section .navbar .dropdown-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    z-index: 1
}

.header-section .navbar .dropdown-toggle::after {
    position: absolute;
    content: "";
    border: none;
    font-family: "FontAwesome";
    font-weight: 400;
    transform: rotate(90deg);
    font-size: 14px;
    right: -10px;
    z-index: -1
}

.header-section .navbar .dropdown-menu {
    padding-right: 20px;
    transition: all .5s;
    transform: translateY(50px) translateX(0)
}

.header-section .navbar .dropdown-menu button, .header-section .navbar .dropdown-menu a {
    padding: 0;
    margin: 0 10px
}

.header-section .navbar .dropdown-menu li {
    margin-top: 9px
}

.header-section .navbar .dropdown-menu, .header-section .navbar .sub-menu {
    display: block;
    opacity: 0;
    visibility: hidden
}

.header-section .navbar .navbar-toggler:focus {
    outline: 0;
    box-shadow: none
}

.header-section .navbar .show-dropdown {
    margin-right: 15px
}

.header-section .navbar .show-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) translateX(0)
}

.header-section .navbar .sub-dropdown {
    position: relative
}

.header-section .navbar .sub-dropdown:hover .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(-5px) translateX(0);
    z-index: 2
}

.header-section .navbar .sub-dropdown .dropdown-toggle::after {
    transform: rotate(0deg)
}

.header-section .navbar .sub-dropdown .sub-menu {
    position: absolute;
    left: 150px;
    top: 0;
    z-index: 1;
    transition: all .5s
}

@media (max-width: 991px) {
    .header-section .navbar .custom-pos {
        position: absolute !important;
        right: 15%;
        top: 6px
    }
}

@media (max-width: 991px)and (max-width: 575px) {
    .header-section .navbar .custom-pos {
        right: 24%;
        top: 10px
    }
}

@media (max-width: 991px) {
    .header-section .navbar .navbar-toggler {
        display: inline-block;
        position: absolute;
        top: 25px
    }

    .header-section .navbar .navbar-brand img {
        max-width: initial
    }

    .header-section .navbar .navbar-toggler span {
        background-color: rgba(var(--s2), 1)
    }

    .header-section .navbar .navbar-collapse .navbar-nav {
        max-height: 250px;
        margin: 20px 0;
        overflow-y: auto;
        margin-bottom: 10px;
        overflow-x: hidden
    }

    .header-section .navbar .navbar-collapse .navbar-nav a, .header-section .navbar .navbar-collapse .navbar-nav button {
        justify-content: start
    }

    .header-section .navbar .dropdown-menu {
        column-count: 1
    }

    .header-section .navbar .sub-dropdown .sub-menu {
        position: initial;
        transition: initial
    }

    .header-section .navbar .dropdown-menu, .header-section .navbar .sub-menu {
        opacity: initial;
        visibility: initial;
        transition: initial;
        display: none
    }

    .header-section .navbar .dropdown-menu {
        transform: initial;
        transition: initial;
        display: none
    }

    .header-section .navbar .dropdown-toggle::after {
        z-index: 0
    }

    .header-section .navbar .dropend {
        margin-left: 18px
    }

    .header-section .navbar .navbar-collapse .right-area {
        margin-left: 0
    }
}

.header-section.header-menu .cmn-head {
    cursor: pointer
}

.header-section.header-menu .cmn-head .icon-area {
    width: 48px;
    height: 48px
}

@media (max-width: 575px) {
    .header-section.header-menu .cmn-head .icon-area {
        width: 35px;
        height: 35px
    }
}

.header-section.header-menu .cmn-head .icon-area i:hover {
    color: rgba(var(--s2), 1)
}

.header-section.header-menu .cmn-head .icon-area .abs-area {
    border: 2px solid rgba(var(--p2), 2);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    top: 0;
    left: -9px
}

.header-section.header-menu .single-item {
    position: relative
}

.header-section.header-menu .single-item.active .main-area {
    opacity: 1;
    visibility: visible;
    transform: translateY(0px)
}

.header-section.header-menu .single-item .main-area {
    position: absolute;
    top: 50px;
    white-space: nowrap;
    right: 0;
    transform: translateY(150px);
    background-color: rgba(var(--n90), 1);
    border: 1px solid var(--n500);
    border-radius: 10px;
    opacity: 0;
    z-index: 2;
    transition: .3s;
    visibility: hidden;
    overflow-x: auto;
    min-width: 300px;
    max-height: 70vh
}

.header-section.header-menu .single-item.cart-area .close-btn {
    right: 15px;
    top: 15px;
    color: var(--n0)
}

.header-section.header-menu .single-item.cart-area .nav-items-wrapper {
    position: fixed;
    top: 0px;
    right: 0px;
    bottom: 0px;
    width: 100%;
    max-width: 500px;
    min-height: 100vh;
    overflow-y: auto;
    background-color: rgba(var(--n8), 1);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(22.5px);
    z-index: 999;
    transform: translateX(100%);
    transition: all 1.3s
}

.header-section.header-menu .single-item.cart-area.active .nav-items-wrapper {
    transform: translateX(0)
}

.header-section.header-menu .single-item.cart-area .single-box {
    border: 1px solid var(--n500);
    border-radius: 5px
}

.header-section.header-menu .single-item.cart-area .end-area {
    color: var(--n0)
}

.header-section.header-menu .single-item.cart-area .icon-box img {
    max-width: 90px;
    min-width: 90px
}

.header-section.header-menu .single-item.cart-area .footer-area .second-btn {
    background-color: var(--n700);
    color: var(--n0)
}

.header-section.header-menu .single-item.cart-area .qtySelector {
    border-radius: 10px
}

.header-section.header-menu .single-item.cart-area .qtySelector i {
    cursor: pointer;
    color: var(--n0);
    min-width: 32px;
    min-height: 32px;
    border-radius: 50%;
    background-color: var(--n100)
}

.header-section.header-menu .single-item.cart-area .qtySelector .qtyValue {
    border: none;
    padding: 5px;
    width: 50px;
    background: rgba(0, 0, 0, 0);
    font-weight: bold;
    color: var(--n0)
}

.header-section .notif-al {
    left: 20%;
    top: 20%
}

.header-section .msg_area {
    box-shadow: 0px 4px 25px 0px rgba(156, 156, 156, .25);
    width: 290px;
    background-color: rgb(var(--p5));
    position: absolute;
    top: 150%;
    left: -360%;
    transform: translateY(10%);
    opacity: 0;
    transition: .8s;
    visibility: hidden;
    z-index: 999
}

@media (max-width: 480px) {
    .header-section .msg_area {
        left: -220%
    }
}

.header-section .msg_area.show {
    transform: translate(0%);
    opacity: 1;
    visibility: visible
}

.header-section .msg_area form {
    border: 1px solid rgb(var(--p1))
}

.header-section .noti-area {
    min-width: 390px;
    box-shadow: 0px 4px 25px 0px rgba(156, 156, 156, .25);
    position: absolute;
    top: 150%;
    left: -470%;
    transform: translateY(10%);
    opacity: 0;
    transition: .8s;
    visibility: hidden
}

@media (max-width: 575px) {
    .header-section .noti-area {
        min-width: 310px
    }
}

.header-section .noti-area.show {
    transform: translate(0%);
    opacity: 1;
    visibility: visible
}

.header-section .noti-area .noti_item .noti_item-group button {
    border-radius: 10px;
    background: #0085ff;
    padding: 8px 15px
}

.header-section .noti-area .noti_item .noti_item-group .noti_item-btn2 {
    background-color: #ffd3c6
}

.header-section .profile_area {
    box-shadow: 0px 4px 25px 0px rgba(156, 156, 156, .25);
    width: 120px;
    position: absolute;
    top: 150%;
    right: 0;
    transform: translateY(10%);
    opacity: 0;
    transition: .7s;
    visibility: hidden
}

.header-section .profile_area.show {
    transform: translate(0%);
    opacity: 1;
    visibility: visible
}

.navbar_top__social-icon a:hover {
    background-color: rgb(var(--p6));
    border: 1px solid rgb(var(--p6))
}

.modal_areastyle .modal-content {
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, .2) !important;
    background-color: rgb(var(--p7));
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08)
}

.modal_areastyle .modal-content .modal-header .btn-close {
    background: none;
    opacity: 1;
    margin: -50px 10px 0 0
}

.modal_areastyle .modal-content .modal-body {
    border: none !important
}

.footer {
    background-image: url("../images/footerbg.png")
}

.footer__decs-subscribe form {
    border: 1px solid rgba(var(--p6), 0.2);
    background: rgba(var(--p6), 0.08)
}

.footer__decs input::placeholder {
    color: rgba(255, 255, 255, .2)
}

.footer__discover .btn.show {
    border: 1px solid rgba(var(--p2), 1)
}

.footer__discover .btn:first-child:active {
    border: 1px solid rgba(var(--p2), 1)
}

.footer__discover-nav ul li {
    margin-left: -15px;
    transition: var(--transition2)
}

.footer__discover-nav ul li i {
    opacity: 0;
    transition: var(--transition2)
}

.footer__discover-nav ul li:hover {
    padding-left: 10px
}

.footer__discover-nav ul li:hover a {
    color: rgb(var(--p1))
}

.footer__discover-nav ul li:hover i {
    opacity: 1
}

.footer__community-item ul li {
    margin-left: -15px;
    transition: var(--transition2)
}

.footer__community-item ul li i {
    opacity: 0;
    transition: var(--transition2)
}

.footer__community-item ul li:hover {
    padding-left: 10px
}

.footer__community-item ul li:hover a {
    color: rgb(var(--p1))
}

.footer__community-item ul li:hover i {
    opacity: 1
}

.footer__hr {
    color: rgba(var(--s2), 1);
    margin-top: -13px;
    width: 84%
}

@media (max-width: 576px) {
    .footer__hr {
        width: 100%
    }
}

.footer__social a i {
    color: rgba(var(--db), 0.5);
    transition: .3s linear
}

.footer__social a i:hover {
    color: rgba(var(--db), 1)
}

.footer__tag ul {
    margin-top: -1px
}

.footer__tag ul li {
    margin-top: 12px
}

.dropdown-toggle::after {
    display: none
}

.cmn_heros {
    background-color: #283d1f;
    background-image: url("../images/herobanner.png") !important;
    background-size: center;
    background-repeat: no-repeat;
    background-position: bottom;
    padding: 85px 0 120px 0
}

@media (max-width: 991px) {
    .cmn_heros {
        padding: 45px 0 80px 0
    }
}

.cmn_heros__title h1 {
    background: linear-gradient(270deg, #C7F801 26.51%, #FACD95 59.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.contact_info__form {
    background: rgba(188, 231, 12, .1)
}

.contact_info__inpuarea-group input {
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, .2);
    background: rgba(255, 255, 255, .08);
    padding: 16px 24px
}

.contact_info__inpuarea-group input::placeholder {
    color: rgba(var(--p6), 0.2)
}

.contact_info__inpuarea textarea {
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, .2);
    background: rgba(255, 255, 255, .08);
    padding: 16px 24px
}

.contact_info__inpuarea textarea::placeholder {
    color: rgba(var(--p6), 0.2)
}

.contact_info__card {
    transition: var(--transition2);
    height: 100%;
    background-color: rgb(var(--p5))
}

.contact_info__card i {
    color: #0b0b12;
    transition: var(--transition2)
}

.contact_info__card:hover {
    background-color: #0b0b12
}

.contact_info__card:hover .contact_info__card-icon i {
    color: #0b0b12
}

.contact_info__card-social i {
    border-radius: 100px;
    border: 1px solid rgb(var(--p1));
    color: rgb(var(--p1));
    transition: .5s linear
}

.contact_info__card-social i:hover {
    background-color: rgb(var(--p1));
    color: rgba(var(--p4), 1)
}

.ready_exhange {
    background-image: url("../images/ready-bg-cmn.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover
}

.not_found {
    height: 100vh;
    overflow-y: scroll
}

.terms_condition__content ol li {
    list-style: inside;
    list-style-type: decimal
}

.rewards_section__tabs .singletab .tablinks .nav-links .active::after {
    content: "";
    position: absolute;
    width: 99%;
    right: 0;
    height: 2px;
    background-color: rgb(var(--p1));
    bottom: -10px
}

.rewards_section__cards-decs h4 {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.rewards_section__cards-decs ul li {
    list-style: inside;
    list-style-type: decimal
}

.rewards_section__upcomming {
    box-shadow: 0px 4px 36px 0px rgba(0, 0, 0, .14)
}

.rewards_section__card-decs ul li {
    list-style: inside;
    list-style-type: decimal;
    color: #ebebec;
    opacity: .8
}

.rewards_section__card-btn button {
    color: #283d1f;
    opacity: .4
}

.rewards_section__card img {
    min-width: 416px
}

@media (max-width: 1399px) {
    .rewards_section__card img {
        min-width: 200px
    }
}

.rewards_faq {
    background-color: #304322;
    background-image: url("../images/faqback-bg.png");
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 0
}

.rewards_faq__content .accordion-single {
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, .2);
    background-color: rgba(255, 255, 255, .08);
    padding: 28px 24px;
    transition: .5s linear
}

@media (max-width: 767px) {
    .rewards_faq__content .accordion-single {
        padding: 16px 12px
    }
}

@media (max-width: 767px) {
    .rewards_faq__content .accordion-single h5 {
        width: 80%
    }
}

.rewards_faq__content .header-area {
    transition: .5s linear;
    cursor: pointer
}

.rewards_faq__content .header-area button {
    transition: .5s linear
}

.rewards_faq__content .header-area button::after {
    content: "";
    font-family: "tabler-icons";
    z-index: 1;
    position: absolute;
    top: -10px;
    right: 0;
    font-size: 20px;
    padding: 8px 10px;
    background-image: none;
    transform: rotate(0deg);
    transition: all .5s ease-in-out;
    border-radius: 50%;
    background-color: #fdb7b7;
    color: rgba(var(--p4), 1)
}

@media (max-width: 767px) {
    .rewards_faq__content .header-area button::after {
        right: -90px;
        padding: 8px
    }
}

@media (max-width: 480px) {
    .rewards_faq__content .header-area button::after {
        right: -50px;
        padding: 8px
    }
}

.rewards_faq__content .content-area {
    display: none;
    padding-top: 16px
}

.rewards_faq__content .active .header-area button {
    transition: .5s linear
}

.rewards_faq__content .active .header-area button::after {
    content: "";
    transform: rotate(180deg);
    background-color: rgba(var(--p4), 1);
    color: #fdb7b7
}

.rewards_faq__content .active .header-area button {
    color: #000
}

.rewards_faq__content .active .content-area p {
    color: #000
}

.rewards_faq__content .accsingle {
    transition: background-color .5s ease
}

.rewards_faq__content .astyle {
    transition: .3s linear
}

.rewards_faq__content .astyle:hover {
    color: rgb(var(--p1))
}

@media (max-width: 767px) {
    .rewards_faq__thumb img {
        width: 100%
    }
}

.rewards_faq__light {
    z-index: -1
}

.pricing_plan__cards {
    overflow: hidden;
    z-index: 0
}

.pricing_plan__cards::before {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    bottom: 0;
    left: 0;
    transition: .5s linear !important;
    background: #c7f801;
    opacity: 0;
    z-index: -1
}

.pricing_plan__cards button:hover {
    background-color: rgba(var(--p4), 1);
    color: #fff !important
}

.pricing_plan__cards:hover::before {
    opacity: 1;
    height: 100%;
    width: 100%
}

.pricing_plan__cards:hover .pricing_plan__cards-btn button {
    transition: .5s linear;
    border: 1px solid rgba(var(--p4), 1)
}

.pricing_plan__cards:hover h4, .pricing_plan__cards:hover h1, .pricing_plan__cards:hover span, .pricing_plan__cards:hover p {
    color: rgba(var(--p4), 1)
}

.pricing_plan__cards:hover .pricing_plan__cards-befit ul li span {
    background-color: rgb(var(--p6))
}

.pricing_plan__cards:hover .pricing_plan__cards-befit ul li span i {
    color: rgba(var(--p4), 1)
}

.pricing_plan__cards:hover .pricing_plan__cards-btn button {
    color: rgba(var(--p4), 1)
}

.pricing_plan__cards h4, .pricing_plan__cards h1, .pricing_plan__cards span, .pricing_plan__cards p {
    transition: .5s linear
}

.pricing_plan__cards-btn button {
    transition: .5s linear
}

.pricing_plan__cards-icon .picon {
    margin-left: -17px
}

.pricing_plan__chooseexpert {
    background: rgba(188, 231, 12, .1)
}

.pricing_plan__choose {
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, .2);
    background: linear-gradient(270deg, rgba(199, 248, 1, 0.24) 6.51%, rgba(250, 205, 149, 0.24) 63.52%)
}

.pricing_plan__choose-switch .switch input {
    display: none
}

.pricing_plan__choose-switch .switch {
    display: inline-block;
    width: 52px;
    height: 24px;
    margin: 4px;
    transform: translateY(50%);
    position: relative
}

.pricing_plan__choose-switch .slider {
    position: absolute;
    top: 0;
    bottom: 0;
    left: -5px;
    right: 0;
    border-radius: 40px;
    box-shadow: 0 0 0 2px #fdb7b7, 0 0 4px #fdb7b7;
    cursor: pointer;
    border: 3px solid rgba(0, 0, 0, 0);
    overflow: hidden;
    transition: .2s
}

.pricing_plan__choose-switch .slider:before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background-color: rgb(var(--p1));
    border-radius: 30px;
    transform: translateX(-30px);
    transition: .2s
}

.pricing_plan__choose-switch input:checked + .slider:before {
    transform: translateX(30px);
    background-color: rgb(var(--p1))
}

.pricing_plan__choose-switch input:checked + .slider {
    box-shadow: 0 0 0 2px rgb(var(--p1)), 0 0 8px rgb(var(--p1))
}

.pricing_plan__choose-switch .switch200 .slider:before {
    width: 200%;
    transform: translateX(-82px)
}

.pricing_plan__choose-monthly {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.blog_details__card {
    border-radius: 20px;
    background: linear-gradient(270deg, rgba(199, 248, 1, 0.6) 6.51%, rgba(250, 205, 149, 0.6) 63.52%) !important
}

.blog_details__card-social a {
    border-radius: 100px;
    background: rgba(11, 11, 18, .32)
}

.blog_details__card-social a:hover {
    background-color: rgb(var(--p4))
}

.blog_details__card-social a:hover i {
    color: rgb(var(--p6))
}

.blog_details__cmncard {
    overflow: hidden;
    z-index: 0
}

.blog_details__cmncard-thumb {
    transition: .5s linear !important;
    border: 1px solid rgba(194, 194, 194, .5)
}

.blog_details__cmncard-thumb img {
    width: 100% !important;
    overflow: hidden
}

.blog_details__cmncard-head .pointed {
    border-radius: 100%;
    padding: 3px 4px;
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    transition: .5s linear !important
}

.blog_details__cmncard-btn a:hover i {
    margin-left: 5px
}

.blog_details__cmncard::before {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    bottom: 0;
    right: 0;
    transition: .5s linear !important;
    background: linear-gradient(180deg, #C7F801 0%, #FACD95 100%);
    opacity: 0;
    z-index: -1
}

.blog_details__cmncard a, .blog_details__cmncard h4, .blog_details__cmncard p, .blog_details__cmncard span, .blog_details__cmncard i {
    transition: .5s linear !important
}

.blog_details__cmncard:hover::before {
    opacity: 1;
    height: 100%;
    width: 100%
}

.blog_details__cmncard:hover a, .blog_details__cmncard:hover h4, .blog_details__cmncard:hover p, .blog_details__cmncard:hover span, .blog_details__cmncard:hover i {
    color: rgba(var(--p4), 1)
}

.blog_details__cmncard:hover .pointed {
    background: rgba(var(--p4), 1)
}

.blog_details__cmncard:hover .blog_details__cmncard-btn a {
    background-color: rgba(var(--p7), 1);
    color: rgb(var(--p6));
    border-color: rgba(var(--p7), 1)
}

.blog_details__cmncard:hover .blog_details__cmncard-btn a i {
    color: rgb(var(--p6)) !important
}

.blog_details__cmncard:hover .blog_details__cmncard-thumb {
    background-color: rgb(var(--p4))
}

.blog_resource__topcard {
    background: linear-gradient(180deg, #C7F801 0%, #FACD95 100%)
}

@media (max-width: 768px) {
    .blog_resource__topcard-thumb img {
        max-width: 100%
    }
}

.blog_resource__topcard .pointed2 {
    border-radius: 100%;
    background-color: rgb(var(--p4));
    color: rgb(var(--p4));
    padding: 2px 3px
}

.buy_crypto {
    background-color: #364d1d
}

.buy_crypto__formitem-select .vodiapicker {
    display: none
}

.buy_crypto__formitem-select .a {
    padding-left: 0px;
    position: absolute;
    right: 0px;
    top: 50px
}

.buy_crypto__formitem-select .a img, .buy_crypto__formitem-select .btn-select img {
    width: 12px
}

.buy_crypto__formitem-select .a li {
    list-style: none;
    padding-top: 5px;
    padding-bottom: 5px
}

.buy_crypto__formitem-select .a li:hover {
    background-color: #f4f3f3
}

.buy_crypto__formitem-select .a li img {
    margin: 5px
}

.buy_crypto__formitem-select .a li span, .buy_crypto__formitem-select .btn-select li span {
    margin-left: 30px
}

.buy_crypto__formitem-select .b {
    display: none;
    width: 100%;
    max-width: 350px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 5px
}

.buy_crypto__formitem-select .open {
    display: show !important
}

.buy_crypto__formitem-select .btn-select {
    margin-top: 10px;
    width: 100%;
    max-width: 350px;
    height: 34px;
    border-radius: 5px
}

.buy_crypto__formitem-select .btn-select li {
    list-style: none;
    float: left;
    padding-bottom: 0px
}

.buy_crypto__formitem-select .btn-select:hover li {
    margin-left: 0px
}

.buy_crypto__formitem-select .btn-select:focus {
    outline: none
}

.buy_crypto__formitem-select .lang-select {
    margin-left: 50px
}

.buy_crypto__formarea-group textarea::placeholder, .buy_crypto__formarea-group input::placeholder {
    color: rgba(var(--p6), 0.2)
}

.buy_Preferred__card {
    overflow: hidden;
    z-index: 0;
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08);
    height: 100%
}

.buy_Preferred__card::before {
    content: "";
    position: absolute;
    width: 1px;
    height: 1px;
    top: 50%;
    right: 0;
    transition: .5s linear !important;
    background: linear-gradient(180deg, #C7F801 0%, #FACD95 100%);
    opacity: 0;
    z-index: -1;
    border-radius: 100%
}

.buy_Preferred__card:hover::before {
    top: 0;
    opacity: 1;
    height: 100%;
    width: 100%;
    border-radius: 0%
}

.buy_Preferred__card:hover span, .buy_Preferred__card:hover h3, .buy_Preferred__card:hover p {
    color: rgb(var(--p4))
}

.buy_Preferred__card span, .buy_Preferred__card h3, .buy_Preferred__card p {
    transition: .5s linear !important
}

@media (max-width: 991px) {
    .buy_Preferred__card-bigthumb {
        width: 100%
    }
}

.buy_crycoinx__card {
    transition: .5s linear !important;
    height: 100%
}

.buy_crycoinx__card:hover {
    background: rgba(255, 255, 255, .08)
}

.buy_crycoinx__card:hover h4 {
    color: rgb(var(--p1))
}

@media (max-width: 480px) {
    .buy_crycoinx__card-icon img {
        width: 30%
    }
}

.counter_section__item {
    transition: .5s linear !important
}

.counter_section__item:hover span, .counter_section__item:hover h4 {
    background: linear-gradient(180deg, #C7F801 0%, #FACD95 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0);
    transition: .5s linear !important
}

.completed_project {
    background-color: #3b521c
}

.completed_project__title h2 {
    background: linear-gradient(270deg, #C7F801 70.51%, #FACD95 93.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.completed_project__item {
    transition: .5s linear
}

.completed_project__item:hover {
    border: 1px solid rgb(var(--p1))
}

.how_join__item {
    overflow: hidden;
    z-index: 0;
    height: 100%
}

.how_join__item-thumb {
    transition: .5s linear
}

.how_join__item::before {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    bottom: 0;
    right: 0;
    transition: .5s linear !important;
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    opacity: 0;
    z-index: -1;
    border-radius: 100%
}

.how_join__item:hover::before {
    top: 0;
    opacity: 1;
    height: 100%;
    width: 100%;
    border-radius: 0%
}

.how_join__item:hover h4, .how_join__item:hover p {
    color: rgb(var(--p9))
}

.how_join__item:hover .how_join__item-thumb {
    background: rgba(var(--p7), 1)
}

.how_join__item h4, .how_join__item p {
    transition: .5s linear
}

.cyber_arena__tphead-name h2 {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 93.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.cyber_arena__tophead-viewbtn a:hover {
    color: initial
}

.cyber_arena__webcnct a {
    transition: .5s linear !important
}

.cyber_arena__webcnct a i {
    transition: .5s linear !important
}

.cyber_arena__webcnct a:hover {
    background-color: rgb(var(--p2));
    color: rgb(var(--p4))
}

.cyber_arena__webcnct a:hover i {
    color: rgb(var(--p4))
}

.cyber_arena__tstyle {
    background: linear-gradient(270deg, #C7F801 89.51%, #FACD95 93.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.cyber_arena__totalcard {
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08)
}

.cyber_arena__social a i {
    transition: .5 linear
}

.cyber_arena__social a:hover {
    background-color: rgb(var(--p2))
}

.cyber_arena__social a:hover i {
    color: rgb(var(--p4))
}

.cyber_arena__countdown {
    transition: all .5s linear
}

.cyber_arena__countdown span {
    transition: all .5s linear
}

.cyber_arena__countdown:hover {
    transition: all .5s linear
}

.cyber_arena__countdown:hover span {
    color: rgba(var(--p2), 1)
}

.cyber_arena__decs h3 {
    background: linear-gradient(270deg, #C7F801 79.51%, #FACD95 98.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.cyber_arena__decs h4 {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 78.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.cyber_arena__decs-card {
    transition: .5s linear;
    border: 1px solid rgba(var(--p1), 0.5);
    overflow: hidden;
    height: 170px
}

@media (max-width: 1199px) {
    .cyber_arena__decs-card {
        height: inherit
    }
}

.cyber_arena__decs-card span {
    transition: .5s linear
}

.cyber_arena__decs-card:hover {
    background: rgba(255, 255, 255, .08);
    border: 1px solid rgba(255, 255, 255, .08);
    transition: .5s linear;
    height: 250px
}

@media (max-width: 1199px) {
    .cyber_arena__decs-card:hover {
        height: inherit
    }
}

.cyber_arena__decs-card:hover span {
    color: rgb(var(--p1))
}

.cyber_arena__decs-card:hover a {
    visibility: visible;
    transition: .5s linear;
    transform: translateY(0)
}

.cyber_arena__decs-card a {
    transition: .3s linear;
    transform: translateY(220px)
}

@media (max-width: 1199px) {
    .cyber_arena__decs-card a {
        transform: inherit
    }
}

@media (max-width: 768px) {
    .cyber_arena__table {
        overflow-x: scroll
    }

    .cyber_arena__table::-webkit-scrollbar {
        height: 6px
    }

    .cyber_arena__table::-webkit-scrollbar-thumb {
        background-color: #fdb7b7;
        border-radius: 2px
    }

    .cyber_arena__table::-webkit-scrollbar-track {
        background-color: rgba(255, 255, 255, .16)
    }
}

@media (max-width: 768px) {
    .cyber_arena__table .table {
        width: 768px
    }
}

.cyber_arena__table .table thead {
    background-color: rgb(var(--p1));
    text-align: center
}

.cyber_arena__table .table thead tr th {
    padding: 16px 0
}

.cyber_arena__table .table thead tr th:first-child {
    border-radius: 20px 0 0 0
}

.cyber_arena__table .table thead tr th:last-child {
    border-radius: 0 20px 0 0
}

.cyber_arena__table .table td {
    color: rgb(var(--p6));
    text-align: center;
    padding: 16px 0;
    border: 1px solid rgba(255, 255, 255, .2)
}

.cyber_arena__table .table tr {
    transition: .5s linear
}

.cyber_arena__table .table tr:hover {
    background: rgba(255, 255, 255, .2)
}

.cyber_arena__table .table .last-footer td {
    color: rgba(var(--p1), 1)
}

.cyber_arena__table table {
    border-collapse: separate;
    border-spacing: 0
}

.cyber_arena__table tr:last-child td:first-child {
    border-bottom-left-radius: 20px
}

.cyber_arena__table tr:last-child td:last-child {
    border-bottom-right-radius: 20px
}

.apex_section__slider .f-dropdown {
    --max-scroll: 3;
    position: relative;
    z-index: 10
}

.apex_section__slider .f-dropdown select {
    display: none
}

.apex_section__slider .f-dropdown > span {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    position: relative;
    color: #bbb;
    border: 1px solid #28330a;
    background: #28330a;
    transition: color .2s ease, border-color .2s ease
}

.apex_section__slider .f-dropdown > span > span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 12px
}

.apex_section__slider .f-dropdown > span img {
    width: 30px;
    margin-right: 10px
}

.apex_section__slider .f-dropdown > span:before, .apex_section__slider .f-dropdown > span:after {
    content: "";
    display: block;
    position: absolute;
    width: 8px;
    height: 2px;
    border-radius: 1px;
    top: 50%;
    right: 5px;
    background: #fff;
    transition: all .3s ease
}

.apex_section__slider .f-dropdown > span:before {
    margin-right: 4px;
    transform: scale(0.96, 0.8) rotate(50deg)
}

.apex_section__slider .f-dropdown > span:after {
    transform: scale(0.96, 0.8) rotate(-50deg)
}

.apex_section__slider .f-dropdown ul {
    margin: 0;
    padding: 0;
    list-style: none;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    max-height: calc(var(--max-scroll) * 46px);
    top: 40px;
    left: 0;
    z-index: 1;
    right: 0;
    background: #28330a;
    border: 1px solid #ccc;
    border-radius: 6px;
    overflow-x: hidden;
    overflow-y: auto;
    transform-origin: 0 0;
    transition: opacity .2s ease, visibility .2s ease, transform .3s cubic-bezier(0.4, 0.6, 0.5, 1.32);
    transform: translate(0, 5px)
}

.apex_section__slider .f-dropdown ul li {
    padding: 0;
    margin: 0
}

.apex_section__slider .f-dropdown ul li a {
    cursor: pointer;
    display: block;
    padding: 8px 12px;
    color: #000;
    text-decoration: none;
    outline: none;
    position: relative;
    transition: all .2s ease;
    display: flex;
    align-items: center
}

.apex_section__slider .f-dropdown ul li a img {
    width: 30px;
    margin-right: 10px
}

.apex_section__slider .f-dropdown ul li a:hover {
    color: #5c6bc0
}

.apex_section__slider .f-dropdown ul li.active a {
    color: #fff;
    background: #303f9f
}

.apex_section__slider .f-dropdown ul li.active a:before, .apex_section__slider .f-dropdown ul li.active a:after {
    --scale: 0.6;
    content: "";
    display: block;
    width: 10px;
    height: 2px;
    position: absolute;
    right: 12px;
    top: 50%;
    opacity: 0;
    background: #fff;
    transition: all .2s ease
}

.apex_section__slider .f-dropdown ul li.active a:before {
    transform: rotate(45deg) scale(var(--scale))
}

.apex_section__slider .f-dropdown ul li.active a:after {
    transform: rotate(-45deg) scale(var(--scale))
}

.apex_section__slider .f-dropdown ul li.active a:hover:before, .apex_section__slider .f-dropdown ul li.active a:hover:after {
    --scale: 0.9;
    opacity: 1
}

.apex_section__slider .f-dropdown ul li:first-child a {
    border-radius: 6px 6px 0 0
}

.apex_section__slider .f-dropdown ul li:last-child a {
    border-radius: 0 0 6px 6px
}

.apex_section__slider .f-dropdown.disabled {
    opacity: .7
}

.apex_section__slider .f-dropdown.disabled > span {
    cursor: not-allowed
}

.apex_section__slider .f-dropdown.filled > span {
    color: #000
}

.apex_section__slider .f-dropdown.open {
    z-index: 15
}

.apex_section__slider .f-dropdown.open > span {
    border-color: #aaa
}

.apex_section__slider .f-dropdown.open > span:before, .apex_section__slider .f-dropdown.open > span:after {
    background: #fff
}

.apex_section__slider .f-dropdown.open > span:before {
    transform: scale(0.96, 0.8) rotate(-50deg)
}

.apex_section__slider .f-dropdown.open > span:after {
    transform: scale(0.96, 0.8) rotate(50deg)
}

.apex_section__slider .f-dropdown.open ul {
    opacity: 1;
    visibility: visible;
    transform: translate(0, 12px);
    transition: opacity .3s ease, visibility .3s ease, transform .3s cubic-bezier(0.4, 0.6, 0.5, 1.32)
}

.apex_section__slider .f-group {
    min-width: 165px;
    margin: 0 auto;
    text-align: left
}

.apex_section__slider .f-group select {
    width: 100%
}

.apex_section__slider .f-control {
    font-size: 14px;
    line-height: normal;
    color: #000;
    display: inline-block;
    background-color: #28330a;
    border: #ccc 1px solid;
    border-radius: 6px;
    padding: 8px 12px;
    outline: none;
    max-width: 250px
}

.apex_section__slider label {
    width: 100%;
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center
}

.apex_section__slideritem-text {
    color: #c2c2c4
}

.apex_section__slideritem .h24 {
    color: #f59571
}

.apex_section__slideritem .box_styleone {
    margin-left: -4px
}

.apex_section__slideritem .box_styletwo {
    margin-right: -26px
}

.apex_section__slideritem .apex_section_sliders_btn {
    top: 20%
}

.apex_section__settingchat a {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

@media (max-width: 1599px) {
    .apex_section .apex_section_sliders {
        margin-left: 80px !important
    }
}

@media (max-width: 480px) {
    .apex_section .apex_section_sliders {
        margin-left: 30px !important
    }
}

@media (max-width: 1599px) {
    .apex_section .slide-buttonpart {
        margin-left: 30px !important
    }
}

@media (max-width: 480px) {
    .apex_section .slide-buttonpart {
        margin-left: 0 !important
    }
}

.apex_section__chartheader {
    background-color: #495230
}

.apex_section__chartheader a {
    font-weight: 600;
    font-family: var(--roboto-font)
}

.apex_section__chartheader a:hover {
    font-weight: 600;
    color: rgb(var(--p1))
}

.apex_section__chartitem-min span {
    transition: .5s linear
}

.apex_section__chartitem-min span:hover {
    color: rgb(var(--p1));
    cursor: pointer
}

.apex_section__tablearea::-webkit-scrollbar {
    height: 6px
}

.apex_section__tablearea::-webkit-scrollbar-thumb {
    background-color: #fdb7b7;
    border-radius: 2px
}

.apex_section__tablearea::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, .16)
}

.apex_section__tablearea-header {
    background-color: #495230
}

@media (max-width: 768px) {
    .apex_section__tablearea-header {
        display: block;
        width: 800px
    }
}

.apex_section__tablearea-hitem a {
    transition: .5s linear
}

.apex_section__tablearea-hitem a:hover {
    color: rgb(var(--p1))
}

@media (max-width: 768px) {
    .apex_section__tablechart {
        display: block;
        width: 800px
    }
}

.apex_section__tablechart table {
    width: 100%
}

.apex_section__tablechart table tr {
    border-bottom: 1px solid rgba(255, 255, 255, .2)
}

.apex_section__tablechart table tr:last-child {
    border-bottom: none
}

.apex_section__tablechart table tr td, .apex_section__tablechart table tr th {
    padding: 18px 0
}

.apex_section__tablechart table tr td .addbtn, .apex_section__tablechart table tr th .addbtn {
    background-color: #495230
}

.apex_section__tablechart table tr th {
    transition: .5s linear
}

.apex_section__tablechart table tr th:hover {
    color: rgb(var(--p1))
}

.apex_section__tablechart table .tablehover {
    transition: .5s linear
}

.apex_section__tablechart table .tablehover td {
    transition: .5s linear
}

.apex_section__tablechart table .tablehover:hover {
    background-color: #495230
}

.apex_section__tablechart table .tablehover:hover td {
    color: rgb(var(--p1))
}

.apex_section__aside-login .modal-header {
    border-color: rgba(255, 255, 255, .08)
}

.apex_section__aside-login .modal-footer {
    border-color: rgba(255, 255, 255, .08)
}

.apex_section__aside-tabs .i-aggres {
    color: #c2c2c4
}

.apex_section__aside-tabs .tabcontents {
    position: relative
}

.apex_section__aside-tabs .tabitem {
    transform: translateY(0);
    position: absolute;
    z-index: -1;
    top: 0;
    width: 100%;
    opacity: 0;
    transition: .5s all
}

.apex_section__aside-tabs .tablinks .active::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: rgba(var(--p1), 1);
    bottom: 0;
    left: 0
}

.apex_section__aside-tabs .tablinks .tablink:hover::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: rgba(var(--p1), 1);
    bottom: 0;
    left: 0
}

.apex_section__aside-tabs .tabitem.active {
    position: initial;
    z-index: 1;
    opacity: 1;
    transform: translateY(0)
}

.apex_section__forms form input::placeholder {
    color: rgba(255, 255, 255, .2)
}

.apex_section__qrcode img {
    max-width: 50% !important
}

.apex_section__heads li button {
    transition: .5s linear
}

.apex_section__heads li button:hover {
    color: rgb(var(--p1))
}

.apex_section__selects option {
    background-color: #283d1f
}

.apex_section__orderbook-table tr td {
    padding: 5px 0
}

.apex_section__parttable .active {
    color: rgb(var(--p1))
}

.apex_section__parttable .custom-tabcontents {
    position: relative
}

.apex_section__parttable .custom-tabitem {
    transform: translateX(100px);
    position: absolute;
    z-index: -1;
    top: 15%;
    width: 100%;
    opacity: 0;
    transition: .5s all
}

.apex_section__parttable .custom-tabitem.active {
    position: initial;
    z-index: 1;
    opacity: 1;
    transform: translateX(0)
}

@media (max-width: 480px) {
    .apex_section__parttable {
        width: 380px
    }
}

.apex_section__parttable .tabcontents {
    position: relative
}

.apex_section__parttable .tabitem {
    transform: translateX(100px);
    position: absolute;
    z-index: -1;
    top: 0;
    width: 100%;
    opacity: 0;
    transition: .5s all
}

.apex_section__parttable .tabitem.active {
    position: initial;
    z-index: 1;
    opacity: 1;
    transform: translateX(0)
}

.apex_section__spcialx::-webkit-scrollbar {
    height: 6px
}

.apex_section__spcialx::-webkit-scrollbar-thumb {
    background-color: #fdb7b7;
    border-radius: 2px
}

.apex_section__spcialx::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, .16)
}

.apex_section__tabletop-up .tabcontents {
    position: relative
}

.apex_section__tabletop-up .tabitem {
    transform: translateY(100px) !important;
    position: absolute;
    z-index: -1;
    top: 0;
    width: 100%;
    opacity: 0;
    transition: .5s all
}

.apex_section__tabletop-up .tabitem.active {
    position: initial;
    z-index: 1;
    opacity: 1;
    transform: translateY(0) !important
}

.countdwon_event {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%)
}

.countdwon_event__part .coundown-container {
    display: flex
}

.countdwon_event__part .countdown-el {
    text-align: center
}

.countdwon_event__part .countdown-el span {
    font-size: 1.5rem
}

.participate {
    z-index: 0
}

.participate .dash-style {
    border: 1px dashed #ebebec;
    width: 32px;
    height: 2px
}

.participate .progress-bar__wrapper {
    position: relative
}

.participate .progress-bar__value {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.participate progress {
    width: 100%;
    height: 6px;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, .2);
    transition: width 300ms ease
}

.participate progress[value]::-webkit-progress-bar {
    width: 100%;
    height: 6px;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, .2);
    transition: width 300ms ease
}

.participate progress[value]::-webkit-progress-value {
    width: 0;
    border-radius: 50px;
    background-color: #fdb7b7;
    transition: width 300ms ease
}

.participate__item {
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08) !important
}

.participate__shapeadd {
    left: 0;
    bottom: 0px;
    height: 120%;
    z-index: -1
}

.event_rules__card-content h5 {
    width: 12%
}

@media (max-width: 1499px) {
    .event_rules__card-content h5 {
        width: 100%
    }
}

.markets_section__card {
    transition: .5s linear
}

.markets_section__card:hover {
    background-color: #283d1f
}

.markets_section__card-head span {
    color: #c2c2c4
}

.markets_section__rcard-selector .f-dropdown {
    --max-scroll: 3;
    position: relative;
    z-index: 10
}

.markets_section__rcard-selector .f-dropdown select {
    display: none
}

.markets_section__rcard-selector .f-dropdown > span {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    position: relative;
    color: #bbb;
    transition: color .2s ease, border-color .2s ease
}

.markets_section__rcard-selector .f-dropdown > span > span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 12px
}

.markets_section__rcard-selector .f-dropdown > span img {
    width: 30px;
    margin-right: 10px
}

.markets_section__rcard-selector .f-dropdown > span:before, .markets_section__rcard-selector .f-dropdown > span:after {
    content: "";
    display: block;
    position: absolute;
    width: 8px;
    height: 2px;
    border-radius: 1px;
    top: 50%;
    right: 5px;
    background: #fff;
    transition: all .3s ease
}

.markets_section__rcard-selector .f-dropdown > span:before {
    margin-right: 4px;
    transform: scale(0.96, 0.8) rotate(50deg)
}

.markets_section__rcard-selector .f-dropdown > span:after {
    transform: scale(0.96, 0.8) rotate(-50deg)
}

.markets_section__rcard-selector .f-dropdown ul {
    margin: 0;
    padding: 0;
    list-style: none;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    max-height: calc(var(--max-scroll) * 46px);
    top: 40px;
    left: 0;
    z-index: 1;
    right: 0;
    background: #28330a;
    border: 1px solid #ccc;
    border-radius: 6px;
    overflow-x: hidden;
    overflow-y: auto;
    transform-origin: 0 0;
    transition: opacity .2s ease, visibility .2s ease, transform .3s cubic-bezier(0.4, 0.6, 0.5, 1.32);
    transform: translate(0, 5px)
}

.markets_section__rcard-selector .f-dropdown ul li {
    padding: 0;
    margin: 0
}

.markets_section__rcard-selector .f-dropdown ul li a {
    cursor: pointer;
    display: block;
    padding: 8px 12px;
    color: #000;
    text-decoration: none;
    outline: none;
    position: relative;
    transition: all .2s ease;
    display: flex;
    align-items: center
}

.markets_section__rcard-selector .f-dropdown ul li a img {
    width: 30px;
    margin-right: 10px
}

.markets_section__rcard-selector .f-dropdown ul li a:hover {
    color: #5c6bc0
}

.markets_section__rcard-selector .f-dropdown ul li.active a {
    color: #fff;
    background: #303f9f
}

.markets_section__rcard-selector .f-dropdown ul li.active a:before, .markets_section__rcard-selector .f-dropdown ul li.active a:after {
    --scale: 0.6;
    content: "";
    display: block;
    width: 10px;
    height: 2px;
    position: absolute;
    right: 12px;
    top: 50%;
    opacity: 0;
    background: #fff;
    transition: all .2s ease
}

.markets_section__rcard-selector .f-dropdown ul li.active a:before {
    transform: rotate(45deg) scale(var(--scale))
}

.markets_section__rcard-selector .f-dropdown ul li.active a:after {
    transform: rotate(-45deg) scale(var(--scale))
}

.markets_section__rcard-selector .f-dropdown ul li.active a:hover:before, .markets_section__rcard-selector .f-dropdown ul li.active a:hover:after {
    --scale: 0.9;
    opacity: 1
}

.markets_section__rcard-selector .f-dropdown ul li:first-child a {
    border-radius: 6px 6px 0 0
}

.markets_section__rcard-selector .f-dropdown ul li:last-child a {
    border-radius: 0 0 6px 6px
}

.markets_section__rcard-selector .f-dropdown.disabled {
    opacity: .7
}

.markets_section__rcard-selector .f-dropdown.disabled > span {
    cursor: not-allowed
}

.markets_section__rcard-selector .f-dropdown.filled > span {
    color: #000
}

.markets_section__rcard-selector .f-dropdown.open {
    z-index: 15
}

.markets_section__rcard-selector .f-dropdown.open > span {
    border-color: #aaa
}

.markets_section__rcard-selector .f-dropdown.open > span:before, .markets_section__rcard-selector .f-dropdown.open > span:after {
    background: #fff
}

.markets_section__rcard-selector .f-dropdown.open > span:before {
    transform: scale(0.96, 0.8) rotate(-50deg)
}

.markets_section__rcard-selector .f-dropdown.open > span:after {
    transform: scale(0.96, 0.8) rotate(50deg)
}

.markets_section__rcard-selector .f-dropdown.open ul {
    opacity: 1;
    visibility: visible;
    transform: translate(0, 12px);
    transition: opacity .3s ease, visibility .3s ease, transform .3s cubic-bezier(0.4, 0.6, 0.5, 1.32)
}

.markets_section__rcard-selector .f-group {
    min-width: 135px;
    margin: 0 auto;
    text-align: left
}

.markets_section__rcard-selector .f-group select {
    width: 100%
}

/*.markets_section__rcard-selector .f-control {
    font-size: 14px;
    line-height: normal;
    color: #000;
    display: inline-block;
    background-color: #28330a;
    border: #ccc 1px solid;
    border-radius: 6px;
    padding: 8px 12px;
    outline: none;
    max-width: 250px
}*/
.markets_section__rcard-selector .f-control {
    font-size: 14px;
    line-height: normal;
    color: #fff;
    display: inline-block;
    background-color: transparent;
    border: #ccc 0px solid;
    border-radius: 6px;
    padding: 8px 12px;
    outline: none;
    max-width: 250px
}

.markets_section__rcard-selector label {
    width: 100%;
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center
}

.markets_section__roundchart .apexcharts-legend {
    overflow: hidden
}

.markets_section__middleicon {
    margin-top: -15px;
    margin-bottom: -15px
}

@media (max-width: 480px) {
    .markets_section__middleicon {
        margin-top: -5px;
        margin-bottom: -5px
    }
}

.markets_section__middleicon .markets_section__middleicon-box {
    transition: .5s linear
}

.markets_section__middleicon .markets_section__middleicon-box:hover {
    transform: rotate(170deg) !important
}

.modal-header {
    border-color: rgba(255, 255, 255, .08)
}

.pools_table {
    background-color: #314423;
    background-image: url("../images/table-pool2.png");
    background-position: bottom;
    background-size: cover;
    background-repeat: no-repeat
}

.pools_table__part .tablinks {
    border-bottom: 2px solid rgba(255, 255, 255, .2);
    padding-bottom: 10px
}

.pools_table__part .tablinks .nav-links {
    position: relative
}

.pools_table__part .tablinks .nav-links:hover::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -12px;
    width: 100%;
    height: 2px;
    background-color: rgb(var(--p1))
}

.pools_table__part .tablinks .active::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -12px;
    width: 100%;
    height: 2px;
    background-color: rgb(var(--p1))
}

.pools_table__totalitem::-webkit-scrollbar {
    height: 6px
}

.pools_table__totalitem::-webkit-scrollbar-thumb {
    background-color: #fdb7b7;
    border-radius: 2px
}

.pools_table__totalitem::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, .16)
}

.pools_table__totalitem table {
    min-width: 1199px !important
}

@media (max-width: 1199px) {
    .pools_table__totalitem table {
        width: 1199px;
        display: block;
        overflow-x: auto
    }
}

.pools_table__totalitem table tr {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    transition: .5s linear
}

@media (max-width: 1199px) {
    .pools_table__totalitem table tr {
        width: 1199px
    }
}

.pools_table__totalitem table tr:hover {
    background-color: rgba(255, 255, 255, .08)
}

.pools_table__totalitem table tr:hover:first-child {
    background-color: rgba(0, 0, 0, 0)
}

.pools_table__totalitem table tr th, .pools_table__totalitem table tr td {
    padding: 16px 0
}

.pools_table__totalitem table tr td {
    width: 400px
}

.pools_table__totalitem-ticonone {
    margin-top: 5px
}

.pools_table__totalitem-ticontwo {
    margin-top: -5px
}

.pools_table__totalitem-img {
    margin-left: -10px
}

.pools_table__pagination nav ul li {
    transition: .5s linear;
    cursor: pointer
}

@media (max-width: 480px) {
    .pools_table__pagination nav ul li {
        padding: 5px 8px !important;
        border-radius: 100%
    }

    .pools_table__pagination nav ul li span {
        padding: 3px 6px
    }
}

.pools_table__pagination nav ul li span, .pools_table__pagination nav ul li i {
    transition: .5s linear
}

.pools_table__pagination nav ul li:hover {
    background-color: rgba(var(--p1), 1)
}

.pools_table__pagination nav ul li:hover span, .pools_table__pagination nav ul li:hover i {
    color: rgb(var(--p4)) !important
}

.closeicon .closeicon-one {
    margin-top: 3px
}

.closeicon .closeicon-two {
    margin-top: -7px
}

@media (max-width: 1199px) {
    .staking_details {
        padding-top: 100px
    }
}

@media (max-width: 991px) {
    .staking_details {
        padding-top: 120px
    }
}

.staking_details__tableone {
    z-index: 1
}

.staking_details__tableone h4 {
    background: linear-gradient(270deg, #C7F801 66.51%, #FACD95 99.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0);
    transition: .5s linear
}

.staking_details__tableone h4, .staking_details__tableone th, .staking_details__tableone td {
    transition: .5s linear
}

.staking_details__tableone::before {
    content: "";
    position: absolute;
    width: 10px;
    height: 10%;
    bottom: 0;
    left: 0;
    transition: .5s linear !important;
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    opacity: 0;
    z-index: -1;
    border-radius: 100%
}

.staking_details__tableone:hover::before {
    top: 0;
    opacity: 1;
    height: 100%;
    width: 100%;
    border-radius: 0%
}

.staking_details__tableone:hover th, .staking_details__tableone:hover td, .staking_details__tableone:hover i, .staking_details__tableone:hover span {
    color: rgb(var(--p4))
}

.staking_details__tableone:hover h4 {
    background: #0b0b12;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.staking_details__tableone:hover .staking_details__tableone-part {
    border: 1px solid rgba(255, 255, 255, .2);
    background: rgba(255, 255, 255, .4);
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08)
}

.staking_details__tableone:hover th {
    border-bottom: 1px solid rgba(40, 61, 31, .24)
}

.staking_details__tableone-scol::-webkit-scrollbar {
    height: 6px
}

.staking_details__tableone-scol::-webkit-scrollbar-thumb {
    background-color: #fdb7b7;
    border-radius: 2px
}

.staking_details__tableone-scol::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, .16)
}

.staking_details__tableone-part {
    border: 1px solid rgba(255, 255, 255, .2);
    background: linear-gradient(270deg, rgba(199, 248, 1, 0.16) 6.51%, rgba(250, 205, 149, 0.16) 63.52%);
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08)
}

@media (max-width: 991px) {
    .staking_details__tableone-part {
        width: 991px
    }
}

.staking_details__tableone-part th {
    padding-bottom: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    font-weight: 600
}

.staking_details__tableone-part td {
    padding: 16px 0
}

.staking_details__tableone-part .copy-text {
    position: relative;
    border-radius: 10px;
    display: flex
}

.staking_details__tableone-part .copy-text button {
    cursor: pointer
}

.staking_details__tableone-part .copy-text button:before {
    content: "Copied";
    position: absolute;
    top: -45px;
    left: 90px;
    background: #283d1f;
    padding: 8px 10px;
    border-radius: 20px;
    font-size: 15px;
    display: none
}

.staking_details__tableone-part .copy-text.active button:before, .staking_details__tableone-part .copy-text.active button:after {
    display: block
}

.staking_details__aside {
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, .08)
}

.staking_details__aside-inputarea span {
    color: #c2c2c4
}

.staking_details__aside-dropdown button {
    margin-top: -10px
}

.staking_details__aside-dropdownitem {
    display: none
}

.what_trending__item {
    transition: .5s linear
}

.what_trending__item:hover {
    background-color: #283d1f
}

.staking_process__item-arrowicon {
    right: -21%;
    top: -6%;
    animation: previewShapeX 4s linear infinite
}

.staking_process__item-arrowicontwo {
    right: -21%;
    top: 20%;
    animation: previewShapeX 4s linear infinite
}

@media (max-width: 991px) {
    .staking_process__item-arrowicon {
        right: -35%;
        top: -6%
    }

    .staking_process__item-arrowicontwo {
        right: -35%;
        top: 20%
    }
}

@media (max-width: 768px) {
    .staking_process__item-arrowicon {
        right: -25%;
        top: -6%
    }
}

.hero_area {
    background-color: #364a2e;
    background-image: url("../images/mainheroarea.png");
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 0
}

.hero_area__content span {
    border-radius: 40px;
    background: rgba(255, 255, 255, .1)
}

.hero_area__content h1 {
    background: linear-gradient(270deg, #c9c9c9 6.51%, #ffffff 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.hero_area__content-btnone:hover i {
    background: rgb(var(--p1));
    color: #384b2e
}

.hero_area__content-btntwo {
    color: rgb(var(--p6)) !important
}

.hero_area__content-btntwo i {
    color: #384b2e !important
}

.hero_area__content-btntwo:hover {
    color: rgb(var(--p4)) !important
}

.hero_area__content-btntwo:hover i {
    background: rgb(var(--p4));
    color: rgb(var(--p6)) !important
}

.hero_area__shape img {
    top: -30%;
    left: 0;
    z-index: -1
}

@media (max-width: 1499px) {
    .hero_area__shape img {
        top: -15%
    }
}

@media (max-width: 991px) {
    .hero_area__shape img {
        top: -10%
    }
}

@media (max-width: 768px) {
    .hero_area__shape img {
        top: 0
    }
}

@media (max-width: 480px) {
    .hero_area__shape img {
        top: 12%
    }
}

.hero_area__lishtone img {
    top: -10%;
    left: -10%;
    z-index: -1
}

.hero_area__lishttwo img {
    top: 0;
    right: 0;
    z-index: -1
}

.hero_area__backgroundrote {
    height: 119.398px;
    transform: rotate(1.368deg);
    background: #798f1d
}

.hero_area__sliders {
    margin-top: -150px;
    background: #fdb7b7;
    height: 119.398px;
    transform: rotate(-3.639deg);
    padding: 34.28px 299.616px 33.135px 299.024px
}

@media (max-width: 768px) {
    .hero_area__sliders {
        padding: 10px
    }
}

.hero_area__counter {
    max-width: 87%
}

@media (max-width: 1399px) {
    .hero_area__counter {
        width: 100%
    }
}

.hero_area__countdown-number span {
    font-size: inherit;
    font-weight: inherit;
    color: inherit
}

.hero_area__thumb-style {
    top: -10%;
    right: -8%
}

@media (max-width: 1399px) {
    .hero_area__thumb-style {
        top: -18%;
        right: -10%
    }
}

@media (max-width: 991px) {
    .hero_area__thumb-style {
        width: 20%
    }
}

@media (max-width: 575px) {
    .hero_area__thumb-style {
        top: -18%;
        right: -3%
    }
}

.brand_slider .swiper-wrapper {
    -webkit-transition-timing-function: linear !important;
    -o-transition-timing-function: linear !important;
    transition-timing-function: linear !important
}

.web3_product__item-btn a:hover {
    transform: rotate(45deg)
}

.web3_product__item:hover .web3_product__item-btn a {
    background-color: rgb(var(--p1))
}

.web3_product__item:hover .web3_product__item-btn a i {
    color: rgb(var(--p4))
}

.market_more .singletab .tablinks .nav-links .tablink {
    transition: .5s linear
}

.market_more .singletab .tablinks .nav-links .tablink:hover {
    color: rgba(var(--p1), 1);
    background: rgba(255, 255, 255, .2)
}

.market_more .singletab .tablinks .nav-links .active {
    color: rgba(var(--p1), 1);
    background: rgba(255, 255, 255, .2)
}

.market_more__item {
    border-radius: 12px;
    transition: .5s linear;
    border: 1px solid rgba(var(--p6), 0.2)
}

.market_more__item .imgstyle {
    margin-left: -20px
}

.market_more__item:hover {
    background: rgba(255, 255, 255, .2);
    border: 1px solid rgba(0, 0, 0, 0) !important
}

.market_more__item:hover a {
    background-color: rgb(var(--p1));
    color: rgb(var(--p4))
}

.get_started {
    background-color: #364a2e;
    background-image: url("../images/howtoget-bg.png");
    background-size: cover
}

.get_started__item {
    height: 100%;
    z-index: 0;
    transition: .5s linear
}

.get_started__item h4, .get_started__item span, .get_started__item button {
    transition: .5s linear
}

.get_started__item::before {
    content: "";
    position: absolute;
    border-radius: 20px;
    width: 100%;
    height: 0%;
    left: 0;
    top: -10%;
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    z-index: -1;
    opacity: 0;
    transition: .5s linear
}

.get_started__item:hover::before {
    content: "";
    position: absolute;
    border-radius: 20px;
    width: 100%;
    height: 120%;
    left: 0;
    top: -10%;
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    z-index: -1;
    opacity: 1
}

.get_started__item:hover .get_started__item-icn {
    background-color: rgba(var(--p4), 0.12)
}

.get_started__item:hover h4, .get_started__item:hover span {
    color: rgb(var(--p4))
}

.get_started__item:hover button {
    background: rgb(var(--p4));
    border: 2px solid rgb(var(--p4));
    color: rgb(var(--p6))
}

.discover_web3__title-left h2 {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

.discover_web3__part ul li button {
    transition: .5s linear
}

.discover_web3__part ul li button:hover {
    color: rgb(var(--p1))
}

.discover_web3 .tabitem::-webkit-scrollbar {
    height: 6px
}

.discover_web3 .tabitem::-webkit-scrollbar-thumb {
    background-color: #fdb7b7;
    border-radius: 2px
}

.discover_web3 .tabitem::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, .16)
}

.earn_crypto {
    background-color: #364a2e;
    background-image: url("../images/earnandsaving.png");
    background-size: cover
}

.earn_crypto__rangeslider input[type=range] {
    font-size: 1.5rem;
    width: 12.5em
}

.earn_crypto__rangeslider input[type=range] {
    color: #fdb7b7;
    --thumb-height: 1.125em;
    --track-height: 8px;
    --track-color: rgba(255, 255, 255, 0.16);
    --brightness-hover: 180%;
    --brightness-down: 80%;
    --clip-edges: 0.125em
}

.earn_crypto__rangeslider input[type=range] {
    position: relative;
    background: rgba(255, 255, 255, 0);
    overflow: hidden
}

.earn_crypto__rangeslider input[type=range], .earn_crypto__rangeslider input[type=range]::-webkit-slider-runnable-track, .earn_crypto__rangeslider input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    transition: all ease 100ms;
    height: var(--thumb-height)
}

.earn_crypto__rangeslider input[type=range]::-webkit-slider-thumb {
    --thumb-radius: calc((var(--thumb-height) * 0.5) - 1px);
    border: 5px solid #fdb7b7;
    --clip-top: calc((var(--thumb-height) - var(--track-height)) * 0.5 - 0.5px);
    --clip-bottom: calc(var(--thumb-height) - var(--clip-top));
    --clip-further: calc(100% + 1px);
    --box-fill: calc(-100vmax - var(--thumb-width, var(--thumb-height))) 0 0 100vmax currentColor;
    width: var(--thumb-width, var(--thumb-height));
    background: linear-gradient(#283D1F 0 0) scroll no-repeat left center/50% calc(var(--track-height) + 1px);
    background-color: #283d1f;
    box-shadow: var(--box-fill);
    border-radius: var(--thumb-width, var(--thumb-height));
    filter: brightness(100%);
    clip-path: polygon(100% -1px, var(--clip-edges) -1px, 0 var(--clip-top), -100vmax var(--clip-top), -100vmax var(--clip-bottom), 0 var(--clip-bottom), var(--clip-edges) 100%, var(--clip-further) var(--clip-further))
}

.earn_crypto__rangeslider input[type=range]::-webkit-slider-runnable-track {
    background: linear-gradient(var(--track-color) 0 0) scroll no-repeat center/100% calc(var(--track-height) + 1px)
}

.earn_crypto .earn_crypto__wrapper {
    position: relative
}

.earn_crypto .earn_crypto__value {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.earn_crypto progress {
    width: 100%;
    height: 8px;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, .2);
    transition: width 300ms ease
}

.earn_crypto progress::before {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #fdb7b7;
    left: 39%;
    top: 10%;
    border-radius: 100%
}

.earn_crypto progress::after {
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    background-color: #283d1f;
    left: 39.8%;
    top: 31%;
    border-radius: 100%
}

@media (max-width: 1199px) {
    .earn_crypto progress::after {
        left: 39.5%
    }
}

@media (max-width: 991px) {
    .earn_crypto progress::after {
        left: 39.7%
    }
}

@media (max-width: 768px) {
    .earn_crypto progress::after {
        left: 39.9%
    }
}

@media (max-width: 480px) {
    .earn_crypto progress::after {
        left: 40.5%
    }
}

.earn_crypto progress[value]::-webkit-progress-bar {
    width: 100%;
    height: 8px;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, .2);
    transition: width 300ms ease
}

.earn_crypto progress[value]::-webkit-progress-value {
    width: 0;
    border-radius: 50px;
    background-color: #fdb7b7;
    transition: width 300ms ease
}

.trust_security {
    background-color: #283d1f;
    background-image: url("../images/noisy-background6.png");
    background-size: cover;
    background-repeat: no-repeat
}

.trust_security__title-tstyle {
    background: linear-gradient(270deg, #C7F801 6.51%, #FACD95 63.52%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgba(0, 0, 0, 0)
}

@media (max-width: 1199px) {
    .trust_security__button {
        display: none
    }
}

@media (max-width: 991px) {
    .trust_security__button {
        display: block
    }
}

@media (max-width: 768px) {
    .trust_security__button {
        right: 20% !important
    }
}

@media (max-width: 575px) {
    .trust_security__button {
        right: 20% !important
    }
}

@media (max-width: 480px) {
    .trust_security__button {
        right: 1% !important
    }
}

.one_stepshop {
    background-color: #151028;
}

.one_stepshop__item {
    height: 100%;
    transition: .5s linear
}

.one_stepshop__item:hover {
    border: 1px solid #c7f801
}

.life_digital {
    background-color: #161618;
    background-image: url("../images/counterbg.png");
    background-size: cover;
    background-repeat: no-repeat
}

/*# sourceMappingURL=style.min.css.map */