"use client";
import {sendRequest} from "./HttpService";

export const fetchWallets = () => {
    return sendRequest('/fetch-wallets', 'POST', {});
};
export const fetchDirects = () => {
    return sendRequest('/fetch-directs', 'POST', {});
};
export const fetchTeamStats = () => {
    return sendRequest('/fetch-team-stats', 'POST', {});
};
export const fetchTreeStructure = (data = {}) => {
    return sendRequest('/fetch-tree-structure', 'POST', data);
};
export const updateDefaultPosition = (data = {}) => {
    return sendRequest('/update-default-position', 'POST', data);
};
export const generateDepositRequest = (data = {}) => {
    return sendRequest('/generate-deposit-request', 'POST', data);
};
export const generateNewContractRequest = (data = {}) => {
    return sendRequest('/generate-new-contract-request', 'POST', data);
};
export const generateEmeraldRequest = (data = {}) => {
    return sendRequest('/generate-emerald-request', 'POST', data);
};
export const generateSellRequest = (data = {}) => {
    return sendRequest('/generate-sell-request', 'POST', data);
};
export const fetchUser = () => {
    return sendRequest('/fetch-user', 'POST', {});
};
export const fetchNotifications = () => {
    return sendRequest('/fetch-notifications', 'POST', {});
};
export const fetchNotification = (data={}) => {
    return sendRequest('/fetch-notification', 'POST', data);
};
export const fetchResources = () => {
    return sendRequest('/fetch-resources', 'POST', {});
};
export const fetchGameStats = () => {
    return sendRequest('/fetch-game-stats', 'POST', {});
};
export const generateTransferRequest = (data={})=>{
    return sendRequest('/generate-transfer-request','POST', data);
};
export const gameParticipate = (game) => {
    return sendRequest('/game-participate', 'POST', {game});
};
export const fetchFinance = (page) => {
    return sendRequest('/fetch-finance', 'POST', {page});
};
export const fetchTransfer = (page) => {
    return sendRequest('/fetch-transfer', 'POST', {page});
};
export const fetchEmeraldRequests = (page) => {
    return sendRequest('/fetch-emerald-requests', 'POST', {page});
};
export const fetchRankBuys = () => {
    return sendRequest('/fetch-rank-buys', 'POST', {});
};
export const fetchBuys = (page) => {
    return sendRequest('/fetch-buys', 'POST', {page});
};
export const fetchCoins = () => {
    return sendRequest('/fetch-coins', 'POST', {});
};
export const fetchSells = (page) => {
    return sendRequest('/fetch-sells', 'POST', {page});
};
export const fetchContracts = (data = {}) => {
    return sendRequest('/fetch-contracts', 'POST', data);
};
export const saveDepositRequest = (data = {}) => {
    return sendRequest('/save-deposit-request', 'POST', data);
};
export const fetchTickets = (data = {}) => {
    return sendRequest('/fetch-tickets', 'POST', data);
};
export const saveTicket = (data = {}) => {
    return sendRequest('/save-ticket', 'POST', data);
};
export const getTicketMessages = (data = {}) => {
    return sendRequest('/get-ticket-messages', 'POST', data);
};
export const saveTransferRequest = (data={})=>{
    return sendRequest('/save-transfer-request','POST', data);
};
export const saveEmeraldRequest = (data={})=>{
    return sendRequest('/save-emerald-request','POST', data);
}
export const sendTicketMessage = (data = {}) => {
    return sendRequest('/save-ticket-message', 'POST', data);
}
export const saveContractRequest = (data = {}) => {
    return sendRequest('/save-contract-request', 'POST', data);
};
export const saveSellRequest = (data = {}) => {
    return sendRequest('/save-sell-request', 'POST', data);
};