"use client";
const KEY_PREFIX = "kimeme-";

const getKeyName = (key) => {
    return KEY_PREFIX + key;
}

export const setLocalStorageItem = (key, val) => {
    window.localStorage.setItem(getKeyName(key), val);
}

export const getLocalStorageItem = (key, defaultValue = null) => {
    let val = window.localStorage.getItem(getKeyName(key));
    if (val === undefined || val === '' || val === 'null') {
        val = null;
    }
    if (val === null) {
        return defaultValue;
    }
    return val;
}

export const removeLocalStorageItem = (key) => {
    window.localStorage.removeItem(getKeyName(key));
}