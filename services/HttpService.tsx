"use client";
import axios from "axios";
import {getLocalStorageItem} from "./LocalStorage";
import {loadEnvironmentProps} from "../context/LoadEnviromentProps";

let axiosClient = axios.create({
    timeout: 100000
});
const config = loadEnvironmentProps('public');
axiosClient.interceptors.request.use(function (config) {
    return config;
}, function (error) {
    return Promise.reject(error);
});

export const sendRequest = (path, method = "GET", data = null, headers = null, contentType = "application/json") => {
    const token=getLocalStorageItem('token');
    return new Promise((resolve, reject) => {
        const headers = {"Content-Type": contentType};
        if(token!=="" && token!==null ) {
            headers['Authorization'] = 'Bearer '+token;
        }
        const params = {
            url: config.API_URL+path,
            method,
            headers,
            params: {},
            data: {}
        };

        if (data !== null) {
            if (method == 'GET') {
                params.params = data;
            } else if (method == 'POST') {
                params.data = data;
            }
        }

        axiosClient(params)
            .then((response) => {
                return resolve(response.data);
            })
            .catch((error) => {
                return reject(error?.response?.data);
            });

    });
};