export interface NFTItem {
  id: string;
  name: string;
  description: string;
  image: string;
  priceInBNB: number;
  creator: string;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  attributes?: NFTAttribute[];
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
}

export interface NFTCollection {
  id: string;
  name: string;
  description: string;
  coverImage: string;
  creator: string;
  totalItems: number;
  floorPriceInBNB: number;
  nfts: NFTItem[];
  createdAt: string;
}

export interface CollectionStats {
  totalVolume: number;
  owners: number;
  listedItems: number;
}
