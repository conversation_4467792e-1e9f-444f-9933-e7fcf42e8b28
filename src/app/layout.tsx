import type { Metadata } from "next";
import '../../public/assets/css/style.min.css';
import UserIdentityComponent from "../../components/UserIdentityComponent";
import Web3ModalProvider from "../../context/Web3Modal";

export const metadata: Metadata = {
  title: "kiMEME",
  description: "Generated by create next app",
};


export default function RootLayout({
  children,

}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
    <head>

      <link rel="stylesheet" href="/assets/css/style.min.css" />
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
      <script src="/assets/js/script.js" />

      <title>{metadata.title!!.toString()}</title>
      <meta name="description" content={metadata.description!!} />
    </head>
      <body >
      <Web3ModalProvider>
        <UserIdentityComponent>{children}</UserIdentityComponent>
      </Web3ModalProvider>
      </body>
    </html>
  );
}
