"use client";
import {useEffect, useState} from "react";
import {useUserIdentity} from "../../hooks/UserIdentity";

import {useWeb3Modal} from '@web3modal/wagmi/react';
import {useDisconnect, useAccount, useSignMessage} from "wagmi";


export default function Home() {
   const {open, close} = useWeb3Modal();
    const {disconnect} = useDisconnect();
    const {address, isConnecting, isDisconnected} = useAccount();
    const { signMessage } = useSignMessage()


    const [newUser, setNewUser] = useState(false);


    const {isLoggedIn, setIsLoggedIn, setUser, setWallets}:any = useUserIdentity();

    useEffect(() => {
        if (address) {
            let signRequest = signMessage({message:'Verify request'});
        }
    }, [address]);
  return (

    <>
        <button className="scrollToTop d-none d-md-flex d-center" aria-label="scroll <PERSON> Button">
            <i className="ti ti-chevron-up fs-four p6-color"></i>
        </button>
        <header className="header-section header-menu w-100 pt-1 pt-lg-0 pb-3 pb-lg-0">
            <div className="navbar_mainhead header-fixed w-100">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-12">
                            <nav className="navbar navbar-expand-lg position-relative py-md-3 py-lg-6 workready">
                                <a href="#" className="navbar-brand d-flex align-items-center gap-2">
                                    <img src="assets/images/logo.png" className="logo" alt="logo" style={{maxHeight: '60px'}} />
                                </a>
                                <div className="collapse navbar-collapse justify-content-between" id="navbar-content">
                                    <ul
                                        className="navbar-nav d-flex align-items-lg-center gap-5 gap-lg-1 gap-xl-4 gap-xxl-7 py-2 py-lg-0 ms-2 ms-xl-10 ms-xxl-20 ps-0 ps-xxl-10 align-self-center">
                                        <li className="dropdown">
                                            <a href="#" className="fs-ten">Home</a>
                                        </li>
                                        <li className="dropdown">
                                            <a href="#" className="fs-ten">What is kiMEME?</a>
                                        </li>
                                        <li className="dropdown">
                                            <a href="#" className="fs-ten">Community</a>
                                        </li>
                                        <li className="dropdown show-dropdown">
                                            <button type="button" aria-label="Navbar Dropdown Button"
                                                    className="dropdown-toggle dropdown-nav d-flex align-items-center fs-ten">kiMEME
                                                <i className="ti ti-chevron-down"></i></button>
                                            <ul className="dropdown-menu">
                                                <li><a className="dropdown-item fs-ten" href="#">Documentation</a>
                                                </li>
                                                <li><a className="dropdown-item fs-ten" href="#">FAQ</a></li>
                                                <li><a className="dropdown-item fs-ten" href="#">How TOS</a>
                                                </li>
                                                <li><a className="dropdown-item fs-ten"
                                                       href="#">Resources</a></li>
                                            </ul>
                                        </li>

                                    </ul>
                                </div>

                                <div
                                    className="right-area custom-pos position-relative d-flex gap-0 gap-lg-2 align-items-center">

                                    <div className="header-section__modalstyle">
                                        <button  onClick={() => open()} type="button"
                                                 className="cmn-btn px-3 px-sm-5 px-md-6 py-2 py-sm-3 d-flex align-items-center gap-1"
                                                 data-bs-toggle="modal" data-bs-target="#exampleModal">
                                            <span className="p7-color fw-semibold d-none d-sm-block">Connect Wallet</span>

                                        </button>
                                        {/*<w3m-button/>*/}
                                    </div>



                                    {/*<div className="header-section__modalstyle">
                                            <button type="button"
                                                    className="cmn-btn px-3 px-sm-5 px-md-6 py-2 py-sm-3 d-flex align-items-center gap-1"
                                                    data-bs-toggle="modal" data-bs-target="#exampleModal">
                                                <span className="p7-color fw-semibold d-none d-sm-block">0xFd976....Ac29B9</span>

                                            </button>
                                        </div>
                                        <div className="header-section__modalstyle">
                                            <button type="button"
                                                    className="cmn-btn px-3 px-sm-5 px-md-6 py-2 py-sm-3 d-flex align-items-center gap-1"
                                                    data-bs-toggle="modal" data-bs-target="#exampleModal">
                                        <span className="p7-color fw-semibold d-none d-sm-block"><i className="fa fa-sign-out" aria-hidden="true"></i>
                                        </span>

                                            </button>
                                        </div>*/}
                                </div>


                                <button className="navbar-toggler" type="button" data-bs-toggle="collapse"
                                        aria-label="Navbar Toggler" data-bs-target="#navbar-content" aria-expanded="true"
                                        id="nav-icon3">
                                    <span></span><span></span><span></span><span></span>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        {/*<Header/>*/}
       {/* <Buy/>
        <Why/>
        <Exchange/>
        <Faq/>
        <Footer/>*/}
    </>

  );
}
