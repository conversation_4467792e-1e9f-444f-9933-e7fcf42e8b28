"use client";
import Image from "next/image";
import { useState } from "react";

export default function Faq() {
    const [activeIndex, setActiveIndex] = useState(null);

    const toggleVisibility = (index) => {
        setActiveIndex(activeIndex === index ? null : index);
    };

    return (
        <>
            <section className="rewards_faq pt-120 pb-120 position-relative">
                <div className="container">
                    <div className="row align-items-center justify-content-center gy-8">
                        <div className="col-xl-7">
                            <div className="rewards_faq__content">
                                <h2 className="mb-5 mb-md-6 wow fadeInUp">FAQs</h2>
                                <p className="roboto mb-8 mb-md-10 wow fadeInUp">Explore our FAQs for fast, informative
                                    answers to
                                    frequently
                                    asked questions and common concerns.</p>
                                <div className="accordion-section">
                                    {[
                                        {title: 'How does kiMEME work?', content: 'kiMEME operates on a decentralized platform powered by Binance\'s blockchain technology. Users can buy, sell, and trade meme-inspired assets using kiMEME tokens. Transactions are recorded securely on the blockchain, ensuring transparency and security.'},
                                        {title: 'What are meme-inspired assets?', content: 'Meme-inspired assets are digital tokens that draw inspiration from popular internet memes. These assets can represent various characters, images, or themes associated with memes and can be traded within the kiMEME ecosystem.'},
                                        {title: 'How can I acquire kiMEME tokens?', content: 'You can acquire kiMEME tokens by participating in the kiMEME ecosystem through activities such as buying, selling, and trading meme-inspired assets. Additionally, you can earn kiMEME tokens through staking or by participating in community events and initiatives.'},
                                        {title: 'Is kiMEME a safe investment?', content: 'As with any investment, there are risks involved in investing in kiMEME tokens. However, we prioritize security and transparency in everything we do. Our platform is built on Binance\'s blockchain, which is known for its robust security features. Additionally, we encourage users to conduct their own research and exercise caution when investing in digital assets.'},
                                        {title: 'How can I get involved with the kiMEME community?', content: 'There are several ways to get involved with the kiMEME community. You can join our social media channels, participate in community discussions and events, and contribute to the development of the kiMEME ecosystem through governance and feedback.'},
                                        {title: 'Can I create my own meme-inspired assets on kiMEME?', content: 'Yes! We welcome creativity and innovation within the kiMEME ecosystem. If you have an idea for a meme-inspired asset, you can propose it to the community for consideration. If approved, you can create and trade your own meme-inspired assets on the kiMEME platform.'}
                                    ].map((item, index) => (
                                        <div key={index} className="accordion-single accsingle mb-5 mb-md-6 wow fadeInUp">
                                            <h5 className="header-area d-flex align-items-center justify-content-between">
                                                <button
                                                    className="accordion-btn d-flex align-items-start position-relative w-100 fs-five fw-bolder text-start"
                                                    type="button" onClick={() => toggleVisibility(index)}>
                                                    { item.title}

                                                </button>
                                            </h5>
                                            <div className="content-area" style={{ display: activeIndex === index ? 'block' : 'none' }}>
                                                <div className="content-body">
                                                    <p>{item.content}</p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="d-flex align-items-center flex-wrap gap-2 wow fadeInUp">
                                    <span>Can't see your question listed above?</span>
                                    <a href="#"
                                       className="d-flex align-items-center gap-1 p6-color astyle">Visit
                                        our Help Center <i className="ti ti-arrow-narrow-right fs-four mt-1"></i></a>
                                </div>
                            </div>
                        </div>
                        <div className="col-xl-5">
                            <div className="rewards_faq__thumb">
                                <img src="assets/images/faq-thumb.png" className="max-un leftright-animation"
                                     alt="Images" />
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
}
