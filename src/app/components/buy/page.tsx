import Image from "next/image";

export default function Buy() {
    return (

        <>
            <section className="pt-120 pb-120"
                     style={{background: 'url("assets/images/mainheroarea.png")', backgroundSize: 'cover' }}>
                <div className="container pt-17 pt-sm-20 pt-lg-0">
                    <div className="row align-items-center">
                        <div className="col-xl-6">
                            <div className="buy_crypto__effort">
                                <h2 className="mb-10 mb-md-15 wow fadeInUp">Effortlessly Buy Crypto Your Way, Anytime,
                                    Anywhere</h2>
                                <div
                                    className="buy_crypto__item d-flex align-items-center flex-wrap flex-sm-nowrap gap-4 gap-sm-5 gap-md-6 mb-10 mb-md-15 wow fadeInUp">
                                    <div className="buy_crypto__item-thumb">
                                        <img src="assets/images/icon/accessible.png" className="max-un" alt="Icons" />
                                    </div>
                                    <div className="buy_crypto__item-content">
                                        <h4 className="mb-5">Accessible</h4>
                                        <p>Easily purchase your favorite cryptocurrencies without any hassle</p>
                                    </div>
                                </div>
                                <div
                                    className="buy_crypto__item d-flex align-items-center flex-wrap flex-sm-nowrap gap-4 gap-sm-5 gap-md-6 mb-10 mb-md-15 wow fadeInUp">
                                    <div className="buy_crypto__item-thumb">
                                        <img src="assets/images/icon/convenient.png" className="max-un" alt="Icons" />
                                    </div>
                                    <div className="buy_crypto__item-content">
                                        <h4 className="mb-5">Convenient</h4>
                                        <p>Choose from a wide range of payment options to buy crypto the way you
                                            prefer.</p>
                                    </div>
                                </div>
                                <div
                                    className="buy_crypto__item d-flex align-items-center flex-wrap flex-sm-nowrap gap-4 gap-sm-5 gap-md-6 mb-10 mb-md-15 wow fadeInUp">
                                    <div className="buy_crypto__item-thumb">
                                        <img src="assets/images/icon/low-cost.png" className="max-un" alt="Icons" />
                                    </div>
                                    <div className="buy_crypto__item-content">
                                        <h4 className="mb-5">Low-cost</h4>
                                        <p>Delight in advantageous pricing featuring competitive rates, low fees, and
                                            steady
                                            conversion rates.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-xl-6">
                            <div className="buy_crypto__formarea p-6 p-px-8 rounded-20 bg7-color wow fadeInUp">
                                <h2 className="mb-3">Buy Crypto</h2>
                                <span className="mb-8 mb-md-10">Buy In Seconds</span>
                                <form>
                                    <div className="buy_crypto__formarea-group mb-5 mb-md-6">
                                        <label className="mb-2">Spend</label>
                                        <div className="d-flex align-items-center br2 p-1 rounded-4 bg1-color">
                                            <input type="text" placeholder="Enter amount" />
                                                <div className="text-end">
                                                    <div
                                                        className="apex_section__slider-selector markets_section__rcard-selector">
                                                        <div className="f-group">
                                                            <select id="select3" className="f-control f-dropdown" defaultValue='DEFAULT'>
                                                                <option value="DEFAULT"
                                                                        data-image="assets/images/icon/currencyCategory71949.png">
                                                                    USDT
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>
                                    </div>
                                    <div className="buy_crypto__formarea-group mb-5 mb-md-6">
                                        <label className="mb-2">Receive</label>
                                        <div className="d-flex align-items-center br2 p-1 rounded-4 mb-2 bg1-color">
                                            <input type="text" placeholder="Enter amount" />
                                                <div className="text-end">
                                                    <div
                                                        className="apex_section__slider-selector markets_section__rcard-selector">
                                                        <div className="f-group">
                                                            <select id="select4" className="f-control f-dropdown" defaultValue='DEFAULT'>
                                                                <option value="DEFAULT"
                                                                        data-image="assets/images/ethereum5051.png">
                                                                    KIM
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>
                                        <span>1 USDT = 126 KIM</span>
                                    </div>
                                    <div className="buy_crypto__formarea-group mb-5 mb-md-6">
                                        <label className="mb-2">Network</label>
                                        <div className="mb-2 br2 p-1 rounded-4 bg1-color">
                                            <div className="text-end">
                                                <div
                                                    className="apex_section__slider-selector markets_section__rcard-selector">
                                                    <div className="f-group">
                                                        <select id="select5" className="f-control f-dropdown" defaultValue='DEFAULT'>
                                                            <option value="DEFAULT"
                                                                    data-image="/assets/images/icon/ethereum5050.png">
                                                                Binance Chain
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="d-flex justify-content-between">
                                             <span>Gass fee</span>
                                            <span className="p1-color">5 USDT</span>
                                        </div>
                                    </div>
                                    <div className="buy_crypto__formarea-group mb-6 mb-md-8">
                                        <label className="mb-2">Wallet Address</label>
                                        <div className="br2 p-1 rounded-4 bg1-color">
                                            <textarea placeholder="Address" cols="15" rows="3"></textarea>
                                        </div>
                                    </div>
                                    <a href="#" className="cmn-btn py-3 px-5 px-md-6 d-block">Next </a>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>

    );
}
