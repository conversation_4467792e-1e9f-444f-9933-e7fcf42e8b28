import React from 'react';

interface ImagePlaceholderProps {
  width?: number;
  height?: number;
  text?: string;
  className?: string;
}

const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({ 
  width = 300, 
  height = 300, 
  text = "NFT",
  className = ""
}) => {
  return (
    <div 
      className={`bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center ${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      <div className="text-center">
        <div className="text-4xl mb-2">🖼️</div>
        <div className="text-gray-600 font-medium">{text}</div>
      </div>
    </div>
  );
};

export default ImagePlaceholder;
