import React, { useState } from 'react';
import { NFTItem as NFTItemType } from '../../../types/nft';
import ImagePlaceholder from './ImagePlaceholder';

interface NFTItemProps {
  nft: NFTItemType;
  onSelect?: (nft: NFTItemType) => void;
}

const NFTItem: React.FC<NFTItemProps> = ({ nft, onSelect }) => {
  const [imageError, setImageError] = useState(false);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Legendary':
        return 'text-yellow-400 border-yellow-400';
      case 'Epic':
        return 'text-purple-400 border-purple-400';
      case 'Rare':
        return 'text-blue-400 border-blue-400';
      case 'Common':
        return 'text-gray-400 border-gray-400';
      default:
        return 'text-gray-400 border-gray-400';
    }
  };

  const handleClick = () => {
    if (onSelect) {
      onSelect(nft);
    }
  };

  return (
    <div 
      className="nft-item bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 cursor-pointer"
      onClick={handleClick}
    >
      {/* NFT Image */}
      <div className="relative aspect-square overflow-hidden">
        {imageError ? (
          <ImagePlaceholder
            width={300}
            height={300}
            text={nft.name}
            className="w-full h-full"
          />
        ) : (
          <img
            src={nft.image}
            alt={nft.name}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
            onError={() => setImageError(true)}
          />
        )}
        <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-semibold border ${getRarityColor(nft.rarity)} bg-black bg-opacity-70`}>
          {nft.rarity}
        </div>
      </div>

      {/* NFT Details */}
      <div className="p-4">
        <h3 className="text-lg font-bold text-gray-900 mb-2 truncate">{nft.name}</h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{nft.description}</p>
        
        {/* Creator */}
        <div className="flex items-center justify-between mb-3">
          <span className="text-xs text-gray-500">Creator</span>
          <span className="text-xs font-medium text-gray-700 truncate max-w-24">{nft.creator}</span>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Price</span>
          <div className="flex items-center gap-1">
            <span className="text-yellow-500 font-bold">⚡</span>
            <span className="text-lg font-bold text-yellow-600">{nft.priceInBNB}</span>
            <span className="text-sm text-gray-500">BNB</span>
          </div>
        </div>

        {/* Attributes Preview */}
        {nft.attributes && nft.attributes.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex flex-wrap gap-1">
              {nft.attributes.slice(0, 2).map((attr, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-xs rounded-full text-gray-600"
                >
                  {attr.trait_type}: {attr.value}
                </span>
              ))}
              {nft.attributes.length > 2 && (
                <span className="px-2 py-1 bg-gray-100 text-xs rounded-full text-gray-600">
                  +{nft.attributes.length - 2} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Action Button */}
        <button className="w-full mt-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-2 px-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-colors duration-300">
          View Details
        </button>
      </div>
    </div>
  );
};

export default NFTItem;
