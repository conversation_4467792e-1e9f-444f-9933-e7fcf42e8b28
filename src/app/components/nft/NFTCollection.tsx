import React, { useState } from 'react';
import { NFTCollection as NFTCollectionType } from '../../../types/nft';
import NFTItem from './NFTItem';
import ImagePlaceholder from './ImagePlaceholder';

interface NFTCollectionProps {
  collection: NFTCollectionType;
  showAllNFTs?: boolean;
  maxNFTsToShow?: number;
}

const NFTCollection: React.FC<NFTCollectionProps> = ({
  collection,
  showAllNFTs = false,
  maxNFTsToShow = 3
}) => {
  const [showAll, setShowAll] = useState(showAllNFTs);
  const [coverImageError, setCoverImageError] = useState(false);
  
  const nftsToDisplay = showAll ? collection.nfts : collection.nfts.slice(0, maxNFTsToShow);
  const hasMoreNFTs = collection.nfts.length > maxNFTsToShow;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="nft-collection bg-gray-50 rounded-xl p-6 mb-8">
      {/* Collection Header */}
      <div className="collection-header mb-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Collection Cover Image */}
          <div className="flex-shrink-0">
            {coverImageError ? (
              <ImagePlaceholder
                width={192}
                height={192}
                text={collection.name}
                className="w-full md:w-48 h-48 rounded-lg shadow-md"
              />
            ) : (
              <img
                src={collection.coverImage}
                alt={collection.name}
                className="w-full md:w-48 h-48 object-cover rounded-lg shadow-md"
                onError={() => setCoverImageError(true)}
              />
            )}
          </div>

          {/* Collection Info */}
          <div className="flex-grow">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">{collection.name}</h2>
            <p className="text-gray-600 mb-4 leading-relaxed">{collection.description}</p>
            
            {/* Collection Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="bg-white rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-900">{collection.totalItems}</div>
                <div className="text-sm text-gray-500">Total Items</div>
              </div>
              <div className="bg-white rounded-lg p-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <span className="text-yellow-500">⚡</span>
                  <span className="text-2xl font-bold text-yellow-600">{collection.floorPriceInBNB}</span>
                </div>
                <div className="text-sm text-gray-500">Floor Price</div>
              </div>
              <div className="bg-white rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-900">{collection.nfts.length}</div>
                <div className="text-sm text-gray-500">Listed</div>
              </div>
              <div className="bg-white rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-gray-900">{formatDate(collection.createdAt)}</div>
                <div className="text-sm text-gray-500">Created</div>
              </div>
            </div>

            {/* Creator Info */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Created by:</span>
              <span className="text-sm font-medium text-gray-700">{collection.creator}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Collection Actions */}
      <div className="flex flex-wrap gap-3 mb-6">
        <button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-2 px-6 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors duration-300">
          View Collection
        </button>
        <button className="bg-white border border-gray-300 text-gray-700 font-semibold py-2 px-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
          Follow Creator
        </button>
        <button className="bg-white border border-gray-300 text-gray-700 font-semibold py-2 px-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
          Share
        </button>
      </div>

      {/* NFTs Grid */}
      <div className="nfts-section">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900">Featured NFTs</h3>
          {hasMoreNFTs && !showAll && (
            <button
              onClick={() => setShowAll(true)}
              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
            >
              View All ({collection.nfts.length})
            </button>
          )}
          {showAll && hasMoreNFTs && (
            <button
              onClick={() => setShowAll(false)}
              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
            >
              Show Less
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {nftsToDisplay.map((nft) => (
            <NFTItem 
              key={nft.id} 
              nft={nft}
              onSelect={(selectedNft) => {
                console.log('Selected NFT:', selectedNft);
                // Handle NFT selection (e.g., open modal, navigate to detail page)
              }}
            />
          ))}
        </div>

        {nftsToDisplay.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">No NFTs available</div>
            <div className="text-gray-500 text-sm">Check back later for new listings</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NFTCollection;
