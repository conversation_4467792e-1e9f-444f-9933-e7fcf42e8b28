import { NFTCollection } from '../types/nft';

export const mockNFTCollections: NFTCollection[] = [
  {
    id: 'kimeme-originals',
    name: 'kiMEME Originals',
    description: 'The first collection of kiMEME NFTs featuring original meme-inspired artwork.',
    coverImage: '/assets/images/nft-collections/kimeme-originals-cover.jpg',
    creator: 'kiMEME Team',
    totalItems: 1000,
    floorPriceInBNB: 0.1,
    createdAt: '2024-01-15',
    nfts: [
      {
        id: 'ko-001',
        name: '<PERSON><PERSON> King',
        description: 'The legendary Doge wearing a crown, ruling the meme kingdom.',
        image: '/assets/images/nfts/doge-king.jpg',
        priceInBNB: 0.5,
        creator: 'kiMEME Team',
        rarity: 'Legendary',
        attributes: [
          { trait_type: 'Background', value: 'Golden' },
          { trait_type: 'Crown', value: 'Diamond' },
          { trait_type: 'Expression', value: 'Majest<PERSON>' }
        ]
      },
      {
        id: 'ko-002',
        name: '<PERSON><PERSON><PERSON> Warrior',
        description: 'A rare Pepe ready for battle in the crypto wars.',
        image: '/assets/images/nfts/pepe-warrior.jpg',
        priceInBNB: 0.3,
        creator: 'kiMEME Team',
        rarity: 'Epic',
        attributes: [
          { trait_type: 'Weapon', value: 'Sword' },
          { trait_type: 'Armor', value: 'Chain Mail' },
          { trait_type: 'Background', value: 'Battlefield' }
        ]
      },
      {
        id: 'ko-003',
        name: 'Cat Vibes',
        description: 'A chill cat enjoying the crypto lifestyle.',
        image: '/assets/images/nfts/cat-vibes.jpg',
        priceInBNB: 0.15,
        creator: 'kiMEME Team',
        rarity: 'Rare',
        attributes: [
          { trait_type: 'Sunglasses', value: 'Cool' },
          { trait_type: 'Background', value: 'Neon' },
          { trait_type: 'Mood', value: 'Relaxed' }
        ]
      }
    ]
  },
  {
    id: 'meme-legends',
    name: 'Meme Legends',
    description: 'Iconic memes that shaped internet culture, now immortalized as NFTs.',
    coverImage: '/assets/images/nft-collections/meme-legends-cover.jpg',
    creator: 'Community',
    totalItems: 500,
    floorPriceInBNB: 0.08,
    createdAt: '2024-02-01',
    nfts: [
      {
        id: 'ml-001',
        name: 'Distracted Boyfriend',
        description: 'The classic meme that started a thousand conversations.',
        image: '/assets/images/nfts/distracted-boyfriend.jpg',
        priceInBNB: 0.25,
        creator: 'MemeArtist',
        rarity: 'Epic',
        attributes: [
          { trait_type: 'Scene', value: 'Street' },
          { trait_type: 'Expression', value: 'Distracted' },
          { trait_type: 'Era', value: '2017' }
        ]
      },
      {
        id: 'ml-002',
        name: 'This is Fine Dog',
        description: 'Everything is fine... or is it?',
        image: '/assets/images/nfts/this-is-fine.jpg',
        priceInBNB: 0.2,
        creator: 'MemeArtist',
        rarity: 'Rare',
        attributes: [
          { trait_type: 'Background', value: 'Fire' },
          { trait_type: 'Mood', value: 'Optimistic' },
          { trait_type: 'Coffee', value: 'Present' }
        ]
      },
      {
        id: 'ml-003',
        name: 'Drake Pointing',
        description: 'The ultimate approval meme in NFT form.',
        image: '/assets/images/nfts/drake-pointing.jpg',
        priceInBNB: 0.18,
        creator: 'MemeArtist',
        rarity: 'Rare',
        attributes: [
          { trait_type: 'Gesture', value: 'Pointing' },
          { trait_type: 'Background', value: 'Studio' },
          { trait_type: 'Approval', value: 'Yes' }
        ]
      }
    ]
  },
  {
    id: 'crypto-memes',
    name: 'Crypto Memes',
    description: 'Memes that capture the wild ride of cryptocurrency culture.',
    coverImage: '/assets/images/nft-collections/crypto-memes-cover.jpg',
    creator: 'CryptoMemeLord',
    totalItems: 750,
    floorPriceInBNB: 0.05,
    createdAt: '2024-03-10',
    nfts: [
      {
        id: 'cm-001',
        name: 'Diamond Hands',
        description: 'HODL strong with these diamond hands!',
        image: '/assets/images/nfts/diamond-hands.jpg',
        priceInBNB: 0.12,
        creator: 'CryptoMemeLord',
        rarity: 'Rare',
        attributes: [
          { trait_type: 'Material', value: 'Diamond' },
          { trait_type: 'Strength', value: 'Unbreakable' },
          { trait_type: 'Strategy', value: 'HODL' }
        ]
      },
      {
        id: 'cm-002',
        name: 'To The Moon',
        description: 'Rocket ship ready for the next bull run!',
        image: '/assets/images/nfts/to-the-moon.jpg',
        priceInBNB: 0.08,
        creator: 'CryptoMemeLord',
        rarity: 'Common',
        attributes: [
          { trait_type: 'Vehicle', value: 'Rocket' },
          { trait_type: 'Destination', value: 'Moon' },
          { trait_type: 'Fuel', value: 'Hopium' }
        ]
      },
      {
        id: 'cm-003',
        name: 'Wen Lambo',
        description: 'The eternal question of every crypto investor.',
        image: '/assets/images/nfts/wen-lambo.jpg',
        priceInBNB: 0.06,
        creator: 'CryptoMemeLord',
        rarity: 'Common',
        attributes: [
          { trait_type: 'Car', value: 'Lamborghini' },
          { trait_type: 'Color', value: 'Yellow' },
          { trait_type: 'Status', value: 'Dreaming' }
        ]
      }
    ]
  }
];
