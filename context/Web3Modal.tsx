'use client'

import React, { ReactNode } from 'react'
import { config, projectId } from '@/../config/Web3Config'

import { createWeb3Modal } from '@web3modal/wagmi/react'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { State, WagmiProvider } from 'wagmi'

const queryClient = new QueryClient()


// Create modal
createWeb3Modal({
    wagmiConfig: config,
    projectId,
})

export default function Web3ModalProvider(
    {
          children,
          initialState
      }: { children: ReactNode, initialState?: State }) {
    return (
        <WagmiProvider config={config} initialState={initialState}>
            <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        </WagmiProvider>
    )
}