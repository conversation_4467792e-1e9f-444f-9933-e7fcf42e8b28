{"name": "kimeme", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.28.14", "@web3modal/wagmi": "^4.1.5", "axios": "^1.6.8", "next": "14.1.4", "react": "^18", "react-dom": "^18", "viem": "^2.9.9", "wagmi": "^2.5.19", "web3": "^4.7.0", "web3-utils": "^4.2.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "encoding": "^0.1.13", "eslint": "^8", "eslint-config-next": "14.1.4", "pino-pretty": "^11.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}