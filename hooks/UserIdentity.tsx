"use client";
import {createContext, useContext, useEffect, useState} from "react";
import {getLocalStorageItem, setLocalStorageItem} from "../services/LocalStorage";
import {fetchContracts, fetchUser, fetchWallets} from "../services/UserService";
import {useDisconnect} from "wagmi";
// import {useBalance, useDisconnect} from "wagmi";

const Identity = createContext({});

export default function UserIdentity({children}) {
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [user, setUser] = useState({});
    const [wallets, setWallets] = useState(null);
    // const { disconnect } = useDisconnect();
    const logout = ()=>{
        setIsLoggedIn(false);
        setUser({});
        setWallets(null);
        setLocalStorageItem('token',null);
        // disconnect();
        return true;
    };
    const updateUserWallet = ()=>{
        fetchWallets().then((res) => {
            if(res.status=="success") {
                setWallets(res.data);
            }
        }).catch((err) => {
            console.log((err));
        });
    };
    const updateUser = ()=>{
        fetchUser().then((res) => {
            if(res.status=="success") {
                setUser(res.data);
            }
        }).catch((err) => {
            console.log((err));
        });
    };
    return (
        <Identity.Provider value={{isLoggedIn,setIsLoggedIn, user,setUser, wallets,setWallets, updateUserWallet, updateUser, logout}}>
            {children}
        </Identity.Provider>
    );
}

export function useUserIdentity()
{
    return useContext(Identity);
}