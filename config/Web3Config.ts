import { defaultWagmiConfig } from '@web3modal/wagmi/react/config'

import { cookieStorage, createStorage } from 'wagmi'
import { bsc } from 'wagmi/chains'

export const projectId = process.env.NEXT_PUBLIC_WEB3_MODAL_PROJECT_ID as string

const metadata = {
    name: 'KiMeMe',
    description: 'Connect your account',
    url: 'http://localhost',
    icons: ['https://avatars.githubusercontent.com/u/********']
}

const chains = [bsc] as const
export const config = defaultWagmiConfig({
    chains,
    projectId,
    metadata,
    ssr: true,
})